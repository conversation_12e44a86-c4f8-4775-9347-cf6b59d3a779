# Firecrawl 本地部署和 API 密钥配置完整指南

## 🔍 核心发现：本地部署认证机制

### **重要结论：本地部署默认不需要 API 密钥**

通过深入分析 Firecrawl 源代码，发现了关键的认证绕过机制：

```typescript
// apps/api/src/controllers/auth.ts (第162-166行)
if (process.env.USE_DB_AUTHENTICATION !== "true") {
  const acuc = mockACUC();
  acuc.is_extract = isExtract;
  return acuc;
}
```

**这意味着：**
- 当 `USE_DB_AUTHENTICATION=false` 时，系统使用模拟认证
- 本地部署默认绕过所有 API 密钥验证
- 所有 API 调用都会成功，无需真实的 API 密钥

## 📋 本地部署配置步骤

### 1. 后端 API 服务配置

#### 创建 `.env` 文件（项目根目录）

```bash
# ===== 必需配置 ======
PORT=3002
HOST=0.0.0.0

# 关闭数据库认证（本地部署关键设置）
USE_DB_AUTHENTICATION=false

# ===== 可选配置 ======

# AI 功能（如需要 LLM 提取功能）
# OPENAI_API_KEY=your-openai-key

# 队列管理面板认证（建议修改）
BULL_AUTH_KEY=your-secure-key

# 其他可选配置...
```

#### 启动后端服务

```bash
# 使用 Docker Compose
docker-compose up -d

# 或者单独启动
cd apps/api
npm install
npm run start:production
```

### 2. 前端 Dashboard 配置

#### 方案 A：无 API 密钥配置（推荐）

在 `apps/firecrawl-dashboard/.env` 中：

```bash
# 本地 API 服务地址
VITE_FIRECRAWL_API_URL=http://localhost:3002

# 不设置 API 密钥（本地部署不需要）
# VITE_FIRECRAWL_API_KEY=

# 应用配置
VITE_APP_TITLE=Firecrawl Dashboard (本地版)
VITE_APP_DESCRIPTION=本地网页抓取和数据提取平台
```

#### 方案 B：使用任意字符串作为密钥

```bash
# 可以使用任意字符串，系统会忽略验证
VITE_FIRECRAWL_API_KEY=local-development-key
VITE_FIRECRAWL_API_URL=http://localhost:3002
```

### 3. 更新前端 API 配置

修改 `apps/firecrawl-dashboard/src/lib/api.ts`：

```typescript
export const getAPI = (apiKey?: string, baseURL?: string): FirecrawlAPI => {
  if (!apiInstance || apiKey) {
    // 本地开发时使用默认密钥
    const key = apiKey || 
                import.meta.env.VITE_FIRECRAWL_API_KEY || 
                'local-dev-key'  // 本地开发默认密钥
    
    const url = baseURL || 
                import.meta.env.VITE_FIRECRAWL_API_URL || 
                'http://localhost:3002'  // 本地 API 默认地址
    
    apiInstance = new FirecrawlAPI(key, url)
  }
  return apiInstance
}
```

### 4. 更新设置页面验证逻辑

修改 `apps/firecrawl-dashboard/src/pages/Settings.tsx`：

```typescript
const handleTestConnection = async () => {
  // 本地部署时，即使没有密钥也允许测试
  const testKey = formData.apiKey || 'local-test-key'
  
  setIsTestingConnection(true)
  try {
    const api = getAPI(testKey, formData.apiUrl)
    const response = await api.health()
    
    if (response.success) {
      toast({
        title: '连接成功',
        description: isLocalDeployment(formData.apiUrl) 
          ? 'API 连接测试成功（本地部署）' 
          : 'API 连接测试成功',
      })
    } else {
      throw new Error(response.error || '连接失败')
    }
  } catch (error) {
    toast({
      title: '连接失败',
      description: error instanceof Error ? error.message : '未知错误',
      variant: 'destructive',
    })
  } finally {
    setIsTestingConnection(false)
  }
}

// 辅助函数：检测是否为本地部署
function isLocalDeployment(url: string): boolean {
  return url.includes('localhost') || url.includes('127.0.0.1') || url.includes('0.0.0.0')
}
```

## 🐳 Docker 部署配置

### 完整的 Docker Compose 配置

创建 `docker-compose.local.yaml`：

```yaml
version: '3.8'

services:
  # Firecrawl API 服务
  firecrawl-api:
    build: apps/api
    container_name: firecrawl-api
    ports:
      - "3002:3002"
    environment:
      - PORT=3002
      - HOST=0.0.0.0
      - USE_DB_AUTHENTICATION=false
      - REDIS_URL=redis://redis:6379
      - PLAYWRIGHT_MICROSERVICE_URL=http://playwright-service:3000/scrape
      - BULL_AUTH_KEY=local-secure-key
    depends_on:
      - redis
      - playwright-service
    networks:
      - firecrawl-network

  # Firecrawl Dashboard
  firecrawl-dashboard:
    build: apps/firecrawl-dashboard
    container_name: firecrawl-dashboard
    ports:
      - "3001:80"
    environment:
      - VITE_FIRECRAWL_API_URL=http://localhost:3002
      - VITE_APP_TITLE=Firecrawl Dashboard (本地版)
    depends_on:
      - firecrawl-api
    networks:
      - firecrawl-network

  # Redis 服务
  redis:
    image: redis:alpine
    container_name: firecrawl-redis
    networks:
      - firecrawl-network

  # Playwright 服务
  playwright-service:
    build: apps/playwright-service-ts
    container_name: firecrawl-playwright
    environment:
      - PORT=3000
    networks:
      - firecrawl-network

networks:
  firecrawl-network:
    driver: bridge
```

### 启动完整服务

```bash
# 启动所有服务
docker-compose -f docker-compose.local.yaml up -d

# 查看服务状态
docker-compose -f docker-compose.local.yaml ps

# 查看日志
docker-compose -f docker-compose.local.yaml logs -f
```

## 🔧 前端应用适配本地部署

### 1. 更新环境检测逻辑

在 `apps/firecrawl-dashboard/src/store/useStore.ts` 中添加：

```typescript
// 检测是否为本地部署
const isLocalDeployment = (url: string) => {
  return url.includes('localhost') || 
         url.includes('127.0.0.1') || 
         url.includes('0.0.0.0')
}

// 更新默认设置
const defaultSettings: Settings = {
  apiKey: '', // 本地部署可以为空
  apiUrl: 'http://localhost:3002', // 默认本地地址
  defaultTimeout: 30000,
  defaultFormats: ['markdown'],
  theme: 'light',
  autoSave: true,
  maxHistoryItems: 100,
}
```

### 2. 更新 Dashboard 页面

修改 `apps/firecrawl-dashboard/src/pages/Dashboard.tsx`：

```typescript
useEffect(() => {
  const checkApiStatus = async () => {
    // 本地部署时，即使没有密钥也尝试连接
    const testKey = settings.apiKey || 'local-test-key'
    
    try {
      const api = getAPI(testKey, settings.apiUrl)
      const response = await api.health()
      setApiStatus(response.success ? 'online' : 'offline')
    } catch {
      setApiStatus('offline')
    }
  }

  checkApiStatus()
}, [settings.apiKey, settings.apiUrl])

// 更新 API 状态显示
{!settings.apiKey && !isLocalDeployment(settings.apiUrl) && (
  <div className="flex items-center space-x-1 text-destructive">
    <AlertCircle className="h-4 w-4" />
    <span className="text-sm">未配置 API 密钥</span>
  </div>
)}

{isLocalDeployment(settings.apiUrl) && (
  <div className="flex items-center space-x-1 text-blue-600">
    <Info className="h-4 w-4" />
    <span className="text-sm">本地部署模式</span>
  </div>
)}
```

### 3. 更新抓取页面验证

修改 `apps/firecrawl-dashboard/src/pages/Scrape.tsx`：

```typescript
const handleScrape = async () => {
  // ... 其他验证逻辑

  // 本地部署时不强制要求 API 密钥
  if (!settings.apiKey && !isLocalDeployment(settings.apiUrl)) {
    toast({
      title: '错误',
      description: '请先在设置中配置 API 密钥',
      variant: 'destructive',
    })
    return
  }

  // 使用默认密钥进行本地开发
  const apiKey = settings.apiKey || 'local-dev-key'
  
  // ... 继续抓取逻辑
}
```

## 🚀 快速启动指南

### 方法 1：完全本地开发

```bash
# 1. 启动后端服务
docker-compose up -d

# 2. 启动前端开发服务器
cd apps/firecrawl-dashboard
npm install
npm run dev

# 访问 http://localhost:3001
```

### 方法 2：完全 Docker 化

```bash
# 1. 构建并启动所有服务
docker-compose -f docker-compose.local.yaml up -d

# 2. 访问服务
# 前端：http://localhost:3001
# API：http://localhost:3002
```

### 方法 3：混合模式

```bash
# 1. 仅启动后端依赖服务
docker-compose up redis playwright-service api -d

# 2. 本地运行前端
cd apps/firecrawl-dashboard
VITE_FIRECRAWL_API_URL=http://localhost:3002 npm run dev
```

## 🔍 验证部署

### 1. 检查 API 服务

```bash
# 健康检查
curl http://localhost:3002/health

# 测试抓取（无需 API 密钥）
curl -X POST http://localhost:3002/v1/scrape \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer any-key-works" \
  -d '{"url": "https://example.com"}'
```

### 2. 检查前端服务

访问 `http://localhost:3001`：
- 仪表板应显示 "API 服务正常"
- 设置页面可以不填写 API 密钥
- 抓取功能应该正常工作

## ⚠️ 注意事项

### 本地部署 vs 云服务的区别

| 特性 | 本地部署 | 云服务 |
|------|----------|--------|
| API 密钥 | 不需要（绕过认证） | 必需 |
| 速率限制 | 无限制 | 根据套餐限制 |
| 高级功能 | 基础功能 | 完整功能 |
| 维护 | 自行维护 | 官方维护 |

### 安全建议

1. **生产环境**：如果要在生产环境使用，建议启用认证
2. **网络访问**：限制 API 服务的网络访问
3. **防火墙**：配置适当的防火墙规则

### 故障排除

1. **API 连接失败**：检查 Docker 服务是否正常运行
2. **前端无法访问**：确认 API URL 配置正确
3. **功能异常**：查看 Docker 日志排查问题

```bash
# 查看服务日志
docker-compose logs api
docker-compose logs firecrawl-dashboard
```

## 📝 总结

**本地部署的关键优势：**
- ✅ 无需 API 密钥，开箱即用
- ✅ 完全离线运行
- ✅ 无速率限制
- ✅ 数据完全本地化

**适用场景：**
- 开发和测试环境
- 内网部署
- 数据安全要求高的环境
- 学习和研究用途

通过以上配置，您可以完全在本地环境中运行 Firecrawl，无需任何外部 API 密钥！
