# Docker Compose configuration for local Firecrawl deployment
# This configuration is optimized for local development and self-hosting

version: '3.8'

services:
  # Redis service
  redis:
    image: redis:alpine
    container_name: firecrawl-redis-local
    restart: unless-stopped
    networks:
      - firecrawl-local
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Playwright service
  playwright-service:
    build: apps/playwright-service-ts
    container_name: firecrawl-playwright-local
    environment:
      - PORT=3000
      - PROXY_SERVER=${PROXY_SERVER}
      - PROXY_USERNAME=${PROXY_USERNAME}
      - PROXY_PASSWORD=${PROXY_PASSWORD}
      - BLOCK_MEDIA=${BLOCK_MEDIA}
    restart: unless-stopped
    networks:
      - firecrawl-local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Firecrawl API service
  api:
    build: apps/api
    container_name: firecrawl-api-local
    ports:
      - "${PORT:-3002}:3002"
    environment:
      # Core settings
      - HOST=0.0.0.0
      - PORT=3002
      - ENV=local
      
      # Authentication (disabled for local deployment)
      - USE_DB_AUTHENTICATION=false
      
      # Redis configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_RATE_LIMIT_URL=redis://redis:6379
      
      # Playwright service
      - PLAYWRIGHT_MICROSERVICE_URL=http://playwright-service:3000/scrape
      
      # Queue management
      - BULL_AUTH_KEY=${BULL_AUTH_KEY:-local-secure-key}
      
      # Optional: AI features
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - MODEL_NAME=${MODEL_NAME}
      - MODEL_EMBEDDING_NAME=${MODEL_EMBEDDING_NAME}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL}
      
      # Optional: Proxy settings
      - PROXY_SERVER=${PROXY_SERVER}
      - PROXY_USERNAME=${PROXY_USERNAME}
      - PROXY_PASSWORD=${PROXY_PASSWORD}
      
      # Optional: Search configuration
      - SEARXNG_ENDPOINT=${SEARXNG_ENDPOINT}
      - SEARXNG_ENGINES=${SEARXNG_ENGINES}
      - SEARXNG_CATEGORIES=${SEARXNG_CATEGORIES}
      
      # Optional: Logging
      - LOGGING_LEVEL=${LOGGING_LEVEL:-info}
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      
      # Optional: Analytics
      - POSTHOG_API_KEY=${POSTHOG_API_KEY}
      - POSTHOG_HOST=${POSTHOG_HOST}
      
      # Optional: PDF parsing
      - LLAMAPARSE_API_KEY=${LLAMAPARSE_API_KEY}
      
    depends_on:
      redis:
        condition: service_healthy
      playwright-service:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - firecrawl-local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Worker service
  worker:
    build: apps/api
    container_name: firecrawl-worker-local
    environment:
      # Same environment as API service
      - HOST=0.0.0.0
      - PORT=3002
      - ENV=local
      - USE_DB_AUTHENTICATION=false
      - REDIS_URL=redis://redis:6379
      - REDIS_RATE_LIMIT_URL=redis://redis:6379
      - PLAYWRIGHT_MICROSERVICE_URL=http://playwright-service:3000/scrape
      - BULL_AUTH_KEY=${BULL_AUTH_KEY:-local-secure-key}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - MODEL_NAME=${MODEL_NAME}
      - MODEL_EMBEDDING_NAME=${MODEL_EMBEDDING_NAME}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL}
      - PROXY_SERVER=${PROXY_SERVER}
      - PROXY_USERNAME=${PROXY_USERNAME}
      - PROXY_PASSWORD=${PROXY_PASSWORD}
      - SEARXNG_ENDPOINT=${SEARXNG_ENDPOINT}
      - SEARXNG_ENGINES=${SEARXNG_ENGINES}
      - SEARXNG_CATEGORIES=${SEARXNG_CATEGORIES}
      - LOGGING_LEVEL=${LOGGING_LEVEL:-info}
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - POSTHOG_API_KEY=${POSTHOG_API_KEY}
      - POSTHOG_HOST=${POSTHOG_HOST}
      - LLAMAPARSE_API_KEY=${LLAMAPARSE_API_KEY}
    depends_on:
      redis:
        condition: service_healthy
      playwright-service:
        condition: service_healthy
      api:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - firecrawl-local
    command: ["pnpm", "run", "workers"]

  # Firecrawl Dashboard
  firecrawl-dashboard:
    build: apps/firecrawl-dashboard
    container_name: firecrawl-dashboard-local
    ports:
      - "${DASHBOARD_PORT:-3001}:80"
    environment:
      # API configuration for local deployment
      - VITE_FIRECRAWL_API_URL=http://localhost:${PORT:-3002}
      - VITE_APP_TITLE=Firecrawl Dashboard (本地版)
      - VITE_APP_DESCRIPTION=本地网页抓取和数据提取平台
    depends_on:
      api:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - firecrawl-local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  firecrawl-local:
    driver: bridge
    name: firecrawl-local-network

volumes:
  redis-data:
    driver: local
