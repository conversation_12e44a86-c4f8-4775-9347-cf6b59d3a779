# 🔥 Firecrawl 中文使用指南

## 目录
- [项目简介](#项目简介)
- [核心特性](#核心特性)
- [快速开始](#快速开始)
- [API 使用指南](#api-使用指南)
- [SDK 使用指南](#sdk-使用指南)
- [高级功能](#高级功能)
- [实际应用场景](#实际应用场景)
- [自托管部署](#自托管部署)

## 项目简介

### 什么是 Firecrawl？

[Firecrawl](https://firecrawl.dev) 是一个强大的 API 服务，能够将任何网站 URL 转换为干净的 markdown 格式或结构化数据。它可以爬取所有可访问的子页面，并为每个页面提供清洁的数据，无需站点地图。

### 主要功能和用途

Firecrawl 为 AI 应用提供干净的网站数据，具备先进的抓取、爬取和数据提取能力。它解决了传统网页抓取中的复杂问题，如：
- 代理处理
- 反机器人机制
- 动态内容（JavaScript 渲染）
- 输出解析
- 任务编排

### 开源 vs 云服务对比

Firecrawl 提供开源版本（AGPL-3.0 许可证）和云服务版本：

**开源版本特性：**
- 基本抓取和爬取功能
- 本地部署
- 社区支持

**云服务版本额外特性：**
- Fire-engine 高级引擎
- 更强的反检测能力
- 更高的成功率
- 专业技术支持
- 更多集成选项

## 核心特性

### 五大核心功能

#### 1. **Scrape（抓取）**
抓取单个 URL 并获取其内容，支持多种格式输出。

#### 2. **Crawl（爬取）**
爬取网站的所有可访问子页面，返回 LLM 就绪格式的内容。

#### 3. **Map（映射）**
快速获取网站的所有 URL 链接。

#### 4. **Search（搜索）**
搜索网络并获取搜索结果的完整内容。

#### 5. **Extract（提取）**
使用 AI 从单个页面、多个页面或整个网站提取结构化数据。

### 强大的能力

- **LLM 就绪格式**：markdown、结构化数据、截图、HTML、链接、元数据
- **处理复杂场景**：代理、反机器人机制、动态内容（JS 渲染）、输出解析、任务编排
- **高度可定制**：排除标签、使用自定义头部爬取认证墙后内容、最大爬取深度等
- **媒体解析**：PDF、DOCX、图片
- **可靠性优先**：专为获取所需数据而设计，无论难度如何
- **页面交互**：点击、滚动、输入、等待等操作
- **批量处理**：使用新的异步端点同时抓取数千个 URL

## 快速开始

### 获取 API 密钥

1. 访问 [Firecrawl 官网](https://firecrawl.dev)
2. 注册账户
3. 获取您的 API 密钥

### 基本使用示例

#### 抓取单个页面

```bash
curl -X POST https://api.firecrawl.dev/v1/scrape \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "url": "https://docs.firecrawl.dev",
      "formats" : ["markdown", "html"]
    }'
```

#### 爬取整个网站

```bash
curl -X POST https://api.firecrawl.dev/v1/crawl \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer fc-YOUR_API_KEY' \
    -d '{
      "url": "https://docs.firecrawl.dev",
      "limit": 10,
      "scrapeOptions": {
        "formats": ["markdown", "html"]
      }
    }'
```

## API 使用指南

### 抓取 API

用于抓取 URL 并获取指定格式的内容。

**请求示例：**
```bash
curl -X POST https://api.firecrawl.dev/v1/scrape \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "url": "https://docs.firecrawl.dev",
      "formats" : ["markdown", "html"]
    }'
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "markdown": "Launch Week I is here! [See our Day 2 Release 🚀]...",
    "html": "<!DOCTYPE html><html lang=\"en\" class=\"light\"...",
    "metadata": {
      "title": "Home - Firecrawl",
      "description": "Firecrawl crawls and converts any website into clean markdown.",
      "language": "en",
      "sourceURL": "https://firecrawl.dev",
      "statusCode": 200
    }
  }
}
```

### 爬取 API

用于爬取 URL 及其所有可访问的子页面。

**提交爬取任务：**
```bash
curl -X POST https://api.firecrawl.dev/v1/crawl \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer fc-YOUR_API_KEY' \
    -d '{
      "url": "https://docs.firecrawl.dev",
      "limit": 10,
      "scrapeOptions": {
        "formats": ["markdown", "html"]
      }
    }'
```

**检查爬取状态：**
```bash
curl -X GET https://api.firecrawl.dev/v1/crawl/123-456-789 \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_API_KEY'
```

### 映射 API

用于映射 URL 并获取网站的所有链接。

```bash
curl -X POST https://api.firecrawl.dev/v1/map \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "url": "https://firecrawl.dev"
    }'
```

### 搜索 API

搜索网络并获取搜索结果的完整内容。

```bash
curl -X POST https://api.firecrawl.dev/v1/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer fc-YOUR_API_KEY" \
  -d '{
    "query": "what is firecrawl?",
    "limit": 5,
    "scrapeOptions": {
      "formats": ["markdown", "links"]
    }
  }'
```

### 提取 API

使用提示词和/或模式从整个网站获取结构化数据。

```bash
curl -X POST https://api.firecrawl.dev/v1/extract \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "urls": [
        "https://firecrawl.dev/*", 
        "https://docs.firecrawl.dev/"
      ],
      "prompt": "提取公司使命、是否开源、是否在 Y Combinator",
      "schema": {
        "type": "object",
        "properties": {
          "company_mission": {"type": "string"},
          "is_open_source": {"type": "boolean"},
          "is_in_yc": {"type": "boolean"}
        },
        "required": ["company_mission", "is_open_source", "is_in_yc"]
      }
    }'
```

## SDK 使用指南

### Python SDK

#### 安装

```bash
pip install firecrawl-py
```

#### 基本使用

```python
from firecrawl.firecrawl import FirecrawlApp
from firecrawl.firecrawl import ScrapeOptions

app = FirecrawlApp(api_key="fc-YOUR_API_KEY")

# 抓取网站
scrape_status = app.scrape_url(
  'https://firecrawl.dev', 
  formats=["markdown", "html"]
)
print(scrape_status)

# 爬取网站
crawl_status = app.crawl_url(
  'https://firecrawl.dev',
  limit=100,
  scrape_options=ScrapeOptions(
    formats=["markdown", "html"],),
  poll_interval=30
)
print(crawl_status)
```

#### 结构化数据提取

```python
from pydantic import BaseModel, Field
from typing import List

class ArticleSchema(BaseModel):
    title: str
    points: int 
    by: str
    commentsURL: str

class TopArticlesSchema(BaseModel):
    top: List[ArticleSchema] = Field(..., description="Top 5 stories")

json_config = JsonConfig(schema=TopArticlesSchema.model_json_schema())

llm_extraction_result = app.scrape_url(
    'https://news.ycombinator.com', 
    formats=["json"], 
    json=json_config
)

print(llm_extraction_result.json)
```

### Node.js SDK

#### 安装

```bash
npm install @mendable/firecrawl-js
```

#### 基本使用

```javascript
import FirecrawlApp, { CrawlParams, CrawlStatusResponse } from '@mendable/firecrawl-js';

const app = new FirecrawlApp({apiKey: "fc-YOUR_API_KEY"});

// 抓取网站
const scrapeResponse = await app.scrapeUrl('https://firecrawl.dev', {
  formats: ['markdown', 'html'],
});

if (scrapeResponse) {
  console.log(scrapeResponse)
}

// 爬取网站
const crawlResponse = await app.crawlUrl('https://firecrawl.dev', {
  limit: 100,
  scrapeOptions: {
    formats: ['markdown', 'html'],
  }
} satisfies CrawlParams, true, 30) satisfies CrawlStatusResponse;

if (crawlResponse) {
  console.log(crawlResponse)
}
```

#### 结构化数据提取

```javascript
import FirecrawlApp from "@mendable/firecrawl-js";
import { z } from "zod";

const app = new FirecrawlApp({
  apiKey: "fc-YOUR_API_KEY"
});

// 定义提取模式
const schema = z.object({
  top: z
    .array(
      z.object({
        title: z.string(),
        points: z.number(),
        by: z.string(),
        commentsURL: z.string(),
      })
    )
    .length(5)
    .describe("Hacker News 前 5 个故事"),
});

const scrapeResult = await app.scrapeUrl("https://news.ycombinator.com", {
  jsonOptions: { extractionSchema: schema },
});

console.log(scrapeResult.data["json"]);
```

## 高级功能

### LLM 提取

使用 LLM 从抓取的页面中提取结构化数据。

#### 使用模式提取

```bash
curl -X POST https://api.firecrawl.dev/v1/scrape \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "url": "https://www.mendable.ai/",
      "formats": ["json"],
      "jsonOptions": {
        "schema": {
          "type": "object",
          "properties": {
            "company_mission": {"type": "string"},
            "supports_sso": {"type": "boolean"},
            "is_open_source": {"type": "boolean"},
            "is_in_yc": {"type": "boolean"}
          },
          "required": ["company_mission", "supports_sso", "is_open_source", "is_in_yc"]
        }
      }
    }'
```

#### 无模式提取

仅使用提示词进行提取，让 LLM 选择数据结构。

```bash
curl -X POST https://api.firecrawl.dev/v1/scrape \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "url": "https://docs.firecrawl.dev/",
      "formats": ["json"],
      "jsonOptions": {
        "prompt": "从页面中提取公司使命。"
      }
    }'
```

### 页面交互（Actions）

在抓取内容之前对网页执行各种操作，特别适用于与动态内容交互。

```bash
curl -X POST https://api.firecrawl.dev/v1/scrape \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
        "url": "google.com",
        "formats": ["markdown"],
        "actions": [
            {"type": "wait", "milliseconds": 2000},
            {"type": "click", "selector": "textarea[title=\"Search\"]"},
            {"type": "wait", "milliseconds": 2000},
            {"type": "write", "text": "firecrawl"},
            {"type": "wait", "milliseconds": 2000},
            {"type": "press", "key": "ENTER"},
            {"type": "wait", "milliseconds": 3000},
            {"type": "click", "selector": "h3"},
            {"type": "wait", "milliseconds": 3000},
            {"type": "screenshot"}
        ]
    }'
```

### 批量抓取

同时抓取多个 URL，类似于爬取端点的工作方式。

```bash
curl -X POST https://api.firecrawl.dev/v1/batch/scrape \
    -H 'Content-Type: application/json' \
    -H 'Authorization: Bearer YOUR_API_KEY' \
    -d '{
      "urls": ["https://docs.firecrawl.dev", "https://docs.firecrawl.dev/sdks/overview"],
      "formats" : ["markdown", "html"]
    }'
```

## 实际应用场景

### 常见用例

1. **内容聚合和监控**
   - 新闻网站内容抓取
   - 竞争对手网站监控
   - 价格监控和比较

2. **数据挖掘和分析**
   - 市场研究数据收集
   - 社交媒体内容分析
   - 学术研究数据收集

3. **AI 和机器学习**
   - 训练数据收集
   - RAG（检索增强生成）系统
   - 知识库构建

4. **业务自动化**
   - 潜在客户信息收集
   - 供应商信息更新
   - 合规性检查

### 集成示例

#### 与 LangChain 集成

```python
from langchain.document_loaders import FireCrawlLoader

loader = FireCrawlLoader(
    api_key="your-api-key",
    url="https://example.com",
    mode="scrape"
)
docs = loader.load()
```

#### 与 Llama Index 集成

```python
from llama_index.readers.web import FireCrawlWebReader

reader = FireCrawlWebReader(api_key="your-api-key")
documents = reader.load_data(url="https://example.com")
```

## 自托管部署

### 环境要求

- Docker
- Docker Compose

### 快速部署

1. **克隆仓库**
```bash
git clone https://github.com/mendableai/firecrawl.git
cd firecrawl
```

2. **设置环境变量**

创建 `.env` 文件：

```bash
# ===== 必需的环境变量 ======
PORT=3002
HOST=0.0.0.0

# 关闭数据库认证（简化部署）
USE_DB_AUTHENTICATION=false

# ===== 可选的环境变量 ======

## === AI 功能（JSON 格式抓取，/extract API）===
# 提供您的 OpenAI API 密钥以启用 AI 功能
# OPENAI_API_KEY=

# 实验性：使用 Ollama
# OLLAMA_BASE_URL=http://localhost:11434/api
# MODEL_NAME=deepseek-r1:7b
# MODEL_EMBEDDING_NAME=nomic-embed-text

## === 代理 ===
# PROXY_SERVER=
# PROXY_USERNAME=
# PROXY_PASSWORD=

## === /search API ===
# 默认情况下，/search API 将使用 Google 搜索
# SEARXNG_ENDPOINT=http://your.searxng.server

## === 其他 ===
# 队列管理面板认证密钥
BULL_AUTH_KEY=CHANGEME

# PDF 解析（可选）
# LLAMAPARSE_API_KEY=
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **验证部署**
```bash
curl http://localhost:3002/health
```

### 配置说明

#### AI 功能配置

要启用 AI 提取功能，需要配置以下之一：

- **OpenAI API**：设置 `OPENAI_API_KEY`
- **Ollama**：设置 `OLLAMA_BASE_URL` 和模型名称
- **其他兼容 API**：设置 `OPENAI_BASE_URL` 和 `OPENAI_API_KEY`

#### 代理配置

如果需要通过代理访问网络：

```bash
PROXY_SERVER=http://proxy.example.com:8080
PROXY_USERNAME=your_username
PROXY_PASSWORD=your_password
```

#### 搜索功能配置

默认使用 Google 搜索，也可以配置 SearXNG：

```bash
SEARXNG_ENDPOINT=http://your.searxng.server
SEARXNG_ENGINES=google,bing
SEARXNG_CATEGORIES=general
```

### 注意事项

1. **功能限制**：自托管版本无法访问 Fire-engine 高级功能
2. **性能考虑**：需要根据使用量调整资源配置
3. **安全性**：确保 API 端点的安全访问
4. **维护**：需要定期更新和维护

## 支持的集成

### LLM 框架
- [LangChain (Python)](https://python.langchain.com/docs/integrations/document_loaders/firecrawl/)
- [LangChain (JavaScript)](https://js.langchain.com/docs/integrations/document_loaders/web_loaders/firecrawl)
- [Llama Index](https://docs.llamaindex.ai/en/latest/examples/data_connectors/WebPageDemo/#using-firecrawl-reader)
- [Crew.ai](https://docs.crewai.com/)
- [Composio](https://composio.dev/tools/firecrawl/all)

### 低代码平台
- [Dify](https://dify.ai/blog/dify-ai-blog-integrated-with-firecrawl)
- [Langflow](https://docs.langflow.org/)
- [Flowise AI](https://docs.flowiseai.com/integrations/langchain/document-loaders/firecrawl)

### 自动化工具
- [Zapier](https://zapier.com/apps/firecrawl/integrations)
- [Pabbly Connect](https://www.pabbly.com/connect/integrations/firecrawl/)

## 许可证

Firecrawl 主要采用 GNU Affero General Public License v3.0 (AGPL-3.0) 许可证，但某些组件采用 MIT 许可证：

- **AGPL-3.0**：适用于项目的所有部分（除非另有说明）
- **MIT**：SDK 和某些 UI 组件

## 贡献和支持

- **GitHub 仓库**：[https://github.com/mendableai/firecrawl](https://github.com/mendableai/firecrawl)
- **文档**：[https://docs.firecrawl.dev](https://docs.firecrawl.dev)
- **Discord 社区**：[https://discord.com/invite/gSmWdAkdwd](https://discord.com/invite/gSmWdAkdwd)
- **官方网站**：[https://firecrawl.dev](https://firecrawl.dev)

---

*本文档基于 Firecrawl 官方文档翻译和整理，如有疑问请参考官方英文文档或联系社区获取支持。*
