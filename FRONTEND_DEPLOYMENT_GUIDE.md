# Firecrawl 前端界面部署指南

## 项目分析总结

### 现有前端界面情况

经过分析，Firecrawl 项目中存在一个基础的前端界面：

**现有界面位置：** `apps/ui/ingestion-ui`

**功能特性：**
✅ 基本的 URL 抓取功能  
✅ 爬取子页面选项  
✅ 高级配置（限制、深度、路径过滤）  
✅ 实时爬取状态显示  

**主要局限性：**
❌ **安全问题** - API 密钥硬编码在客户端  
❌ **功能不完整** - 只支持 scrape/crawl，缺少 map/search/extract  
❌ **API 版本过时** - 使用 v0 API 而非 v1  
❌ **结果展示有限** - 只支持 markdown 格式  
❌ **缺少高级功能** - 无 LLM 提取、批量处理、WebSocket 等  

## 新前端应用设计

基于分析结果，我们创建了一个全新的、功能完整的前端应用：

### 技术栈
- **框架：** React 18 + TypeScript + Vite
- **UI 库：** Tailwind CSS + shadcn/ui
- **状态管理：** Zustand + React Query
- **路由：** React Router
- **构建工具：** Vite + Docker

### 核心功能

#### 🔥 完整的 API 支持
- **Scrape** - 抓取单个页面
- **Crawl** - 爬取整个网站
- **Map** - 映射网站链接
- **Search** - 搜索网络内容
- **Extract** - AI 驱动的数据提取

#### 🛡️ 安全性改进
- 环境变量管理 API 密钥
- 支持代理模式保护敏感信息
- CORS 配置和安全头部

#### 🎨 用户体验
- 响应式设计，支持移动端
- 深色/浅色主题切换
- 实时进度显示
- 完善的错误处理

#### 📊 数据管理
- 历史记录管理
- 结果导出功能
- 多格式支持（Markdown、JSON、HTML）

## 部署方案

### 方案一：独立部署（推荐）

#### 1. 快速启动

```bash
# 进入前端应用目录
cd apps/firecrawl-dashboard

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 API 密钥

# 启动开发服务器
npm run dev
```

访问 `http://localhost:3001`

#### 2. Docker 部署

```bash
# 构建镜像
docker build -t firecrawl-dashboard ./apps/firecrawl-dashboard

# 运行容器
docker run -p 3001:80 \
  -e VITE_FIRECRAWL_API_URL=http://localhost:3002 \
  firecrawl-dashboard
```

### 方案二：集成部署

#### 1. 使用扩展的 Docker Compose

```bash
# 启动完整服务（包括新前端）
docker-compose -f docker-compose.yaml -f docker-compose.dashboard.yaml up -d

# 仅启动新前端
docker-compose -f docker-compose.dashboard.yaml up firecrawl-dashboard
```

#### 2. 使用反向代理

```bash
# 启动包含 Nginx 代理的完整服务
docker-compose -f docker-compose.dashboard.yaml --profile proxy up -d
```

访问：
- **新前端界面：** `http://localhost:3001`
- **API 服务：** `http://localhost:3002`
- **代理统一入口：** `http://localhost` (如果启用了 proxy profile)

### 方案三：生产环境部署

#### 1. 环境配置

```bash
# 生产环境变量
export VITE_FIRECRAWL_API_KEY=your-production-api-key
export VITE_FIRECRAWL_API_URL=https://your-api-domain.com
```

#### 2. 构建和部署

```bash
# 构建生产版本
npm run build

# 使用 Nginx 部署
docker run -d \
  --name firecrawl-dashboard \
  -p 80:80 \
  -v $(pwd)/dist:/usr/share/nginx/html \
  nginx:alpine
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_FIRECRAWL_API_KEY` | Firecrawl API 密钥 | - |
| `VITE_FIRECRAWL_API_URL` | API 服务地址 | `https://api.firecrawl.dev` |
| `VITE_APP_TITLE` | 应用标题 | `Firecrawl Dashboard` |

### API 代理配置

如果需要隐藏 API 密钥，可以配置后端代理：

```javascript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3002',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

### CORS 配置

在 Firecrawl API 服务中添加 CORS 支持：

```javascript
// 在 API 服务中
app.use(cors({
  origin: ['http://localhost:3001', 'https://your-frontend-domain.com'],
  credentials: true
}))
```

## 功能使用指南

### 1. 首次设置

1. 访问设置页面 (`/settings`)
2. 输入您的 Firecrawl API 密钥
3. 测试 API 连接
4. 保存设置

### 2. 抓取页面

1. 访问抓取页面 (`/scrape`)
2. 输入要抓取的 URL
3. 点击"开始抓取"
4. 查看结果并下载

### 3. 查看历史

1. 访问历史记录页面 (`/history`)
2. 查看所有操作记录
3. 导出历史数据
4. 管理记录

## 故障排除

### 常见问题

#### 1. API 连接失败

**症状：** 显示"API 服务离线"

**解决方案：**
- 检查 API 密钥是否正确
- 确认 API URL 是否可访问
- 检查网络连接

#### 2. CORS 错误

**症状：** 浏览器控制台显示 CORS 错误

**解决方案：**
- 在 API 服务中配置 CORS
- 使用代理模式
- 检查域名配置

#### 3. 构建失败

**症状：** `npm run build` 失败

**解决方案：**
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install

# 检查 Node.js 版本
node --version  # 应该是 18+
```

### 日志查看

```bash
# 查看容器日志
docker logs firecrawl-dashboard

# 查看 Nginx 日志
docker exec firecrawl-dashboard tail -f /var/log/nginx/access.log
```

## 性能优化

### 1. 构建优化

```bash
# 启用生产优化
npm run build

# 分析包大小
npm install -g webpack-bundle-analyzer
npx webpack-bundle-analyzer dist/static/js/*.js
```

### 2. 缓存配置

在 Nginx 配置中添加缓存：

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 压缩配置

```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

## 安全建议

### 1. API 密钥管理

- 使用环境变量存储密钥
- 不要在代码中硬编码密钥
- 定期轮换 API 密钥

### 2. 网络安全

- 使用 HTTPS
- 配置安全头部
- 限制 CORS 来源

### 3. 访问控制

- 配置防火墙规则
- 使用反向代理
- 实施速率限制

## 升级指南

### 从现有界面迁移

1. 备份现有配置
2. 部署新前端应用
3. 更新 API 配置
4. 测试所有功能
5. 切换流量

### 版本更新

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
npm update

# 重新构建
npm run build

# 重启服务
docker-compose restart firecrawl-dashboard
```

## 支持和反馈

如果您在部署或使用过程中遇到问题：

1. 查看本指南的故障排除部分
2. 检查项目的 GitHub Issues
3. 提交新的 Issue 或 Pull Request

---

**注意：** 这个新的前端界面是对现有 `apps/ui/ingestion-ui` 的完整替代和升级，提供了更安全、更完整、更现代化的用户体验。
