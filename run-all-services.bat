@echo off
chcp 65001 >nul
echo ========================================
echo    Starting All Firecrawl Services
echo ========================================
echo.

REM Check if Redis is running
echo Checking Redis...
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo Starting Redis server...
    start "Redis Server" redis-server
    timeout /t 3 /nobreak >nul
)

REM Start Playwright service
echo Starting Playwright service...
start "Playwright Service" cmd /k "cd apps\playwright-service-ts && pnpm start"
timeout /t 5 /nobreak >nul

REM Start API service
echo Starting API service...
start "API Service" cmd /k "cd apps\api && pnpm run dev"
timeout /t 5 /nobreak >nul

REM Start Frontend service
echo Starting Frontend service...
start "Frontend Service" cmd /k "cd apps\firecrawl-dashboard && npm run dev"
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo    All Services Started!
echo ========================================
echo.
echo Services are starting in separate windows:
echo   - Redis Server
echo   - Playwright Service (port 3000)
echo   - API Service (port 3002)
echo   - Frontend Service (port 3001)
echo.
echo Wait a moment for all services to fully start, then access:
echo   Frontend Dashboard: http://localhost:3001
echo   API Health Check:   http://localhost:3002/health
echo.
echo Press any key to open the dashboard...
pause >nul
start http://localhost:3001
