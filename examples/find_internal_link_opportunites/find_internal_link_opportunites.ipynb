#%%
import os
import datetime
import time
from firecrawl import FirecrawlApp
import json
import anthropic
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Retrieve API keys from environment variables
anthropic_api_key = os.getenv("ANTHROPIC_API_KEY") or ""
firecrawl_api_key = os.getenv("FIRECRAWL_API_KEY") or ""
# Set variables
blog_url="https://mendable.ai/blog"

# Set up anthropic client
client = anthropic.Anthropic(
    api_key=anthropic_api_key,
)

# Initialize the FirecrawlApp with your API key
app = FirecrawlApp(api_key=firecrawl_api_key)

#%%
# Crawl a website
params = {
    'crawlOptions': {
        'limit': 100
    },
    "pageOptions": {
        "onlyMainContent": True
    }
}
crawl_result = app.crawl_url(blog_url, params=params)

#%%
potential_links = []

if crawl_result:
    print("Collecting potential links from crawl_result:")
    
    for item in crawl_result:
        metadata = item["metadata"]
        og_url = metadata.get("ogUrl")
        title = metadata.get("title")
        if og_url and title and og_url != blog_url:
            potential_links.append({"url": og_url, "title": title})
    
    print(f"Collected {len(potential_links)} potential links:")
    for link in potential_links:
        print(f"URL: {link['url']}, Title: {link['title']}")
        
else:
    print("crawl_result is empty or None")
#%%
import json
import csv

# Assuming we have the following variables from the previous code:
# crawl_result, client, potential_links

# Convert potential_links to a JSON string
potential_links_json = json.dumps(potential_links, indent=2)

# Prepare CSV file
csv_filename = "link_suggestions.csv"
csv_headers = ["Source Blog Title", "Source Blog URL", "Target Phrase", "Suggested Link URL"]

# Write headers to the CSV file
with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
    csvwriter = csv.writer(csvfile)
    csvwriter.writerow(csv_headers)

# Loop through each blog post content
for item in crawl_result:
    current_blog_url = item["metadata"].get("ogUrl", "")
    if current_blog_url == blog_url:
        continue
    current_blog_content = item["content"]
    current_blog_title = item["metadata"].get("title", "")

    prompt_instructions = f"""Given this blog post from {current_blog_url} called '{current_blog_title}', analyze the following blog content. Identify 0 to 3 of phrases (5 words max) from the <blog_content> inside of the middle of the article that could be linked to other blog posts from the list of potential links provided inside of <potential_links>. Return a JSON object structured as follows:

    {{
      "link_suggestions": [
        {{
          "target_phrase": "the EXACT phrase from the <blog_content> to be linked to one of the links in <potential_links> (5 words max)",
          "suggested_link_url": "url of the suggested internal link from <potential_links>",
        }}
      ],
      "metadata": {{
        "source_blog_url": "{current_blog_url}",
        "source_blog_title": "{current_blog_title}",
      }}
    }}

    Ensure that you provide the EXACT phrase from <blog_content> in target_phrase (5 words max) to locate each suggestion in the blog content without using character positions. Your target phrases must NOT be a title!

    Blog Content:
    <blog_content>
    {current_blog_content}
    </blog_content>

    Potential Links:
    <potential_links>
    {potential_links_json}
    </potential_links>

    GO AND ONLY RETURN THE JSON NOTHING ELSE:"""

    try:
        message = client.messages.create(
            model="claude-3-5-sonnet-20240620",
            max_tokens=1024,
            messages=[
                {"role": "user", "content": prompt_instructions}
            ]
        )
        
        # Extract the JSON string from the TextBlock
        json_string = message.content[0].text
        
        # Parse the JSON response
        response_json = json.loads(json_string)
        
        # Write suggestions to CSV
        for suggestion in response_json['link_suggestions']:
            print("Suggestion for: " + current_blog_title )
            print("Blog phrase: " + suggestion['target_phrase']) 
            print("Internal Link: " + suggestion['suggested_link_url'])
            print("---\n\n")

            # Open the CSV file in append mode and write the new row
            with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
                csvwriter = csv.writer(csvfile)
                csvwriter.writerow([
                    response_json['metadata']['source_blog_title'],
                    response_json['metadata']['source_blog_url'],
                    suggestion['target_phrase'],
                    suggestion['suggested_link_url'],
                ])
      
    except json.JSONDecodeError:
        print(f"Error parsing JSON response for blog {current_blog_title}")
        print("Raw response:", message.content)
    except Exception as e:
        print(f"Error processing blog {current_blog_title}: {str(e)}")
    

print(f"Finished processing all blog posts. Results saved to {csv_filename}")