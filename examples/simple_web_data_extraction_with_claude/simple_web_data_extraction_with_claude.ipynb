#%% md
# Web Scraping and Extraction with <PERSON><PERSON><PERSON><PERSON> and <PERSON>

This notebook demonstrates how to use Firecrawl to scrape web content and <PERSON> to extract structured data from it.
#%% md
## Step 1: Import Required Libraries
#%%
import os
import json
from firecrawl import FirecrawlApp
from anthropic import Anthropic
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
#%% md
## Step 2: Set Up API Keys and URL
#%%
# Retrieve API keys from environment variables
anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
firecrawl_api_key = os.getenv("FIRECRAWL_API_KEY")

# Set the URL to scrape
url = "https://mendable.ai"  # Replace with the actual URL you want to scrape

print(f"URL to scrape: {url}")
#%% md
## Step 3: Initialize Firecrawl and Anthropic Clients
#%%
# Initialize FirecrawlApp and Anthropic client
firecrawl_app = FirecrawlApp(api_key=firecrawl_api_key)
anthropic_client = Anthropic(api_key=anthropic_api_key)

print("Firecrawl and Anthropic clients initialized.")
#%% md
## Step 4: Scrape the URL using Firecrawl
#%%
# Scrape the URL using Firecrawl
page_content = firecrawl_app.scrape_url(url, params={"pageOptions": {"onlyMainContent": True}})

print(f"Page content scraped. Length: {len(page_content['content'])} characters")
#%% md
## Step 5: Prepare the Prompt for Claude
#%%
# Prepare the prompt for Claude
prompt = f"""Analyze the following webpage content and extract the following information:
1. The title of the page
2. Whether the company is part of Y Combinator (YC)
3. Whether the company/product is open source

Return the information in JSON format with the following schema:
{{
    "main_header_title": string,
    "is_yc_company": boolean,
    "is_open_source": boolean
}}

Webpage content:
{page_content['content']}

Return only the JSON, nothing else."""

print("Prompt prepared for Claude.")
#%% md
## Step 6: Query Claude
#%%
# Query Claude
response = anthropic_client.messages.create(
    model="claude-3-opus-20240229",
    max_tokens=1000,
    messages=[
        {"role": "user", "content": prompt}
    ]
)

print("Claude response received.")
#%% md
## Step 7: Parse and Display the Result
#%%
# Parse and print the result
result = json.loads(response.content[0].text)
print(json.dumps(result, indent=2))