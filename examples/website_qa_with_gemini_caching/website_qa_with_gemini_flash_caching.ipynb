#%%
import os
import datetime
import time
import google.generativeai as genai
from google.generativeai import caching
from dotenv import load_dotenv
from firecrawl import FirecrawlApp
import json

# Load environment variables
load_dotenv()

# Retrieve API keys from environment variables
google_api_key = os.getenv("GOOGLE_API_KEY")
firecrawl_api_key = os.getenv("FIRECRAWL_API_KEY")

# Configure the Google Generative AI module with the API key
genai.configure(api_key=google_api_key)

# Initialize the FirecrawlApp with your API key
app = FirecrawlApp(api_key=firecrawl_api_key)

#%%
# Crawl a website
crawl_url = 'https://dify.ai/'
params = {
   
    'crawlOptions': {
        'limit': 100
    }
}
crawl_result = app.crawl_url(crawl_url, params=params)

if crawl_result is not None:
    # Convert crawl results to JSON format, excluding 'content' field from each entry
    cleaned_crawl_result = [{k: v for k, v in entry.items() if k != 'content'} for entry in crawl_result]

    # Save the modified results as a text file containing JSON data
    with open('crawl_result.txt', 'w') as file:
        file.write(json.dumps(cleaned_crawl_result, indent=4))
else:
    print("No data returned from crawl.")

#%%
# Upload the video using the Files API
text_file = genai.upload_file(path="crawl_result.txt")

# Wait for the file to finish processing
while text_file.state.name == "PROCESSING":
    print('Waiting for file to be processed.')
    time.sleep(2)
    text_file = genai.get_file(text_file.name)


#%%
# Create a cache with a 5 minute TTL
cache = caching.CachedContent.create(
    model="models/gemini-1.5-flash-002",
    display_name="website crawl testing again", # used to identify the cache
    system_instruction="You are an expert at this website, and your job is to answer user's query based on the website you have access to.",
    contents=[text_file],
    ttl=datetime.timedelta(minutes=15),
)
# Construct a GenerativeModel which uses the created cache.
model = genai.GenerativeModel.from_cached_content(cached_content=cache)


#%%
# Query the model
response = model.generate_content(["What powers website scraping with Dify?"])
response_dict = response.to_dict()
response_text = response_dict['candidates'][0]['content']['parts'][0]['text']
print(response_text)
