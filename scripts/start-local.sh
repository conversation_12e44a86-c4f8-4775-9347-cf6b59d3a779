#!/bin/bash

# Firecrawl 本地部署启动脚本
# 此脚本将启动完整的 Firecrawl 本地环境

set -e

echo "🔥 启动 Firecrawl 本地部署..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ 错误：Docker 未安装。请先安装 Docker。"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ 错误：Docker Compose 未安装。请先安装 Docker Compose。"
    exit 1
fi

# 检查是否存在 .env 文件，如果不存在则创建
if [ ! -f .env ]; then
    echo "📝 创建 .env 配置文件..."
    cat > .env << EOF
# Firecrawl 本地部署配置
PORT=3002
DASHBOARD_PORT=3001
USE_DB_AUTHENTICATION=false
BULL_AUTH_KEY=local-secure-key-$(date +%s)

# 可选：AI 功能配置
# OPENAI_API_KEY=your-openai-key

# 可选：代理配置
# PROXY_SERVER=
# PROXY_USERNAME=
# PROXY_PASSWORD=

# 可选：日志级别
LOGGING_LEVEL=info
EOF
    echo "✅ 已创建 .env 配置文件"
fi

# 检查前端环境配置
if [ ! -f apps/firecrawl-dashboard/.env ]; then
    echo "📝 创建前端环境配置..."
    cp apps/firecrawl-dashboard/.env.local apps/firecrawl-dashboard/.env
    echo "✅ 已创建前端环境配置"
fi

# 停止可能正在运行的服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.local.yaml down --remove-orphans 2>/dev/null || true

# 构建并启动服务
echo "🚀 构建并启动服务..."
docker-compose -f docker-compose.local.yaml up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查 API 服务
API_PORT=${PORT:-3002}
if curl -f http://localhost:$API_PORT/health >/dev/null 2>&1; then
    echo "✅ API 服务正常运行 (http://localhost:$API_PORT)"
else
    echo "❌ API 服务启动失败"
    echo "📋 查看 API 服务日志："
    docker-compose -f docker-compose.local.yaml logs api
    exit 1
fi

# 检查前端服务
DASHBOARD_PORT=${DASHBOARD_PORT:-3001}
if curl -f http://localhost:$DASHBOARD_PORT/health >/dev/null 2>&1; then
    echo "✅ 前端服务正常运行 (http://localhost:$DASHBOARD_PORT)"
else
    echo "❌ 前端服务启动失败"
    echo "📋 查看前端服务日志："
    docker-compose -f docker-compose.local.yaml logs firecrawl-dashboard
    exit 1
fi

echo ""
echo "🎉 Firecrawl 本地部署启动成功！"
echo ""
echo "📱 访问地址："
echo "   前端界面: http://localhost:$DASHBOARD_PORT"
echo "   API 服务: http://localhost:$API_PORT"
echo "   API 文档: http://localhost:$API_PORT/docs (如果可用)"
echo ""
echo "🔧 管理命令："
echo "   查看日志: docker-compose -f docker-compose.local.yaml logs -f"
echo "   停止服务: docker-compose -f docker-compose.local.yaml down"
echo "   重启服务: docker-compose -f docker-compose.local.yaml restart"
echo ""
echo "💡 提示："
echo "   - 本地部署无需 API 密钥"
echo "   - 所有数据存储在本地"
echo "   - 可以在设置页面配置 AI 功能"
echo ""
