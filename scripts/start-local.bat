@echo off
chcp 65001 >nul
REM Firecrawl Local Deployment Script for Windows
REM This script will start the complete Firecrawl local environment

echo Starting Firecrawl local deployment...

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo Error: Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Check if .env file exists, create if not
if not exist .env (
    echo Creating .env configuration file...
    (
        echo # Firecrawl Local Deployment Configuration
        echo PORT=3002
        echo DASHBOARD_PORT=3001
        echo USE_DB_AUTHENTICATION=false
        echo BULL_AUTH_KEY=local-secure-key-%RANDOM%
        echo.
        echo # Optional: AI Features Configuration
        echo # OPENAI_API_KEY=your-openai-key
        echo.
        echo # Optional: Proxy Configuration
        echo # PROXY_SERVER=
        echo # PROXY_USERNAME=
        echo # PROXY_PASSWORD=
        echo.
        echo # Optional: Logging Level
        echo LOGGING_LEVEL=info
    ) > .env
    echo Created .env configuration file successfully
)

REM Check frontend environment configuration
if not exist apps\firecrawl-dashboard\.env (
    echo Creating frontend environment configuration...
    copy apps\firecrawl-dashboard\.env.local apps\firecrawl-dashboard\.env >nul
    echo Created frontend environment configuration successfully
)

REM Stop any running services
echo Stopping existing services...
docker-compose -f docker-compose.local.yaml down --remove-orphans >nul 2>&1

REM Build and start services
echo Building and starting services...
docker-compose -f docker-compose.local.yaml up -d --build

REM Wait for services to start
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service status
echo Checking service status...

REM Check API service
set API_PORT=3002
curl -f http://localhost:%API_PORT%/health >nul 2>&1
if errorlevel 1 (
    echo API service failed to start
    echo View API service logs:
    docker-compose -f docker-compose.local.yaml logs api
    pause
    exit /b 1
) else (
    echo API service is running normally at http://localhost:%API_PORT%
)

REM Check frontend service
set DASHBOARD_PORT=3001
curl -f http://localhost:%DASHBOARD_PORT%/health >nul 2>&1
if errorlevel 1 (
    echo Frontend service failed to start
    echo View frontend service logs:
    docker-compose -f docker-compose.local.yaml logs firecrawl-dashboard
    pause
    exit /b 1
) else (
    echo Frontend service is running normally at http://localhost:%DASHBOARD_PORT%
)

echo.
echo Firecrawl local deployment started successfully!
echo.
echo Access URLs:
echo    Frontend: http://localhost:%DASHBOARD_PORT%
echo    API Service: http://localhost:%API_PORT%
echo.
echo Management Commands:
echo    View logs: docker-compose -f docker-compose.local.yaml logs -f
echo    Stop services: docker-compose -f docker-compose.local.yaml down
echo    Restart services: docker-compose -f docker-compose.local.yaml restart
echo.
echo Tips:
echo    - No API key required for local deployment
echo    - All data is stored locally
echo    - You can configure AI features in the settings page
echo.
pause
