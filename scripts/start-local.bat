@echo off
REM Firecrawl 本地部署启动脚本 (Windows)
REM 此脚本将启动完整的 Firecrawl 本地环境

echo 🔥 启动 Firecrawl 本地部署...

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：Docker 未安装。请先安装 Docker Desktop。
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：Docker Compose 未安装。请先安装 Docker Compose。
    pause
    exit /b 1
)

REM 检查是否存在 .env 文件，如果不存在则创建
if not exist .env (
    echo 📝 创建 .env 配置文件...
    (
        echo # Firecrawl 本地部署配置
        echo PORT=3002
        echo DASHBOARD_PORT=3001
        echo USE_DB_AUTHENTICATION=false
        echo BULL_AUTH_KEY=local-secure-key-%RANDOM%
        echo.
        echo # 可选：AI 功能配置
        echo # OPENAI_API_KEY=your-openai-key
        echo.
        echo # 可选：代理配置
        echo # PROXY_SERVER=
        echo # PROXY_USERNAME=
        echo # PROXY_PASSWORD=
        echo.
        echo # 可选：日志级别
        echo LOGGING_LEVEL=info
    ) > .env
    echo ✅ 已创建 .env 配置文件
)

REM 检查前端环境配置
if not exist apps\firecrawl-dashboard\.env (
    echo 📝 创建前端环境配置...
    copy apps\firecrawl-dashboard\.env.local apps\firecrawl-dashboard\.env >nul
    echo ✅ 已创建前端环境配置
)

REM 停止可能正在运行的服务
echo 🛑 停止现有服务...
docker-compose -f docker-compose.local.yaml down --remove-orphans >nul 2>&1

REM 构建并启动服务
echo 🚀 构建并启动服务...
docker-compose -f docker-compose.local.yaml up -d --build

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...

REM 检查 API 服务
set API_PORT=3002
curl -f http://localhost:%API_PORT%/health >nul 2>&1
if errorlevel 1 (
    echo ❌ API 服务启动失败
    echo 📋 查看 API 服务日志：
    docker-compose -f docker-compose.local.yaml logs api
    pause
    exit /b 1
) else (
    echo ✅ API 服务正常运行 ^(http://localhost:%API_PORT%^)
)

REM 检查前端服务
set DASHBOARD_PORT=3001
curl -f http://localhost:%DASHBOARD_PORT%/health >nul 2>&1
if errorlevel 1 (
    echo ❌ 前端服务启动失败
    echo 📋 查看前端服务日志：
    docker-compose -f docker-compose.local.yaml logs firecrawl-dashboard
    pause
    exit /b 1
) else (
    echo ✅ 前端服务正常运行 ^(http://localhost:%DASHBOARD_PORT%^)
)

echo.
echo 🎉 Firecrawl 本地部署启动成功！
echo.
echo 📱 访问地址：
echo    前端界面: http://localhost:%DASHBOARD_PORT%
echo    API 服务: http://localhost:%API_PORT%
echo.
echo 🔧 管理命令：
echo    查看日志: docker-compose -f docker-compose.local.yaml logs -f
echo    停止服务: docker-compose -f docker-compose.local.yaml down
echo    重启服务: docker-compose -f docker-compose.local.yaml restart
echo.
echo 💡 提示：
echo    - 本地部署无需 API 密钥
echo    - 所有数据存储在本地
echo    - 可以在设置页面配置 AI 功能
echo.
pause
