name: Deploy Images to GHCR

env:
  DOTNET_VERSION: '6.0.x'

on:
  push:
    branches:
      - main
    paths:
      - apps/api/**
  workflow_dispatch:

jobs:
      push-app-image:
        runs-on: ubuntu-latest
        defaults:
          run:
            working-directory: './apps/api'
        steps:
          - name: 'Checkout GitHub Action'
            uses: actions/checkout@main

          - name: 'Set up Docker Buildx'
            uses: docker/setup-buildx-action@v3

          - name: 'Login to GitHub Container Registry'
            uses: docker/login-action@v3
            with:
              registry: ghcr.io
              username: ${{github.actor}}
              password: ${{secrets.GITHUB_TOKEN}}

          - name: 'Build and Push Image'
            uses: docker/build-push-action@v5
            with:
              context: ./apps/api
              push: true
              tags: ghcr.io/mendableai/firecrawl:latest
              cache-from: type=registry,ref=ghcr.io/mendableai/firecrawl:buildcache
              cache-to: type=registry,ref=ghcr.io/mendableai/firecrawl:buildcache,mode=max