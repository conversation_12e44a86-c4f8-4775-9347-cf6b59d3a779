@echo off
chcp 65001 >nul
echo ========================================
echo    Firecrawl Local Development Setup
echo ========================================
echo.

REM Check Node.js
echo [1/8] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed.
    echo Please install Node.js 18+ from: https://nodejs.org/
    pause
    exit /b 1
)
for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo Node.js version: %NODE_VERSION%

REM Check pnpm
echo [2/8] Checking pnpm...
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo Installing pnpm...
    npm install -g pnpm
) else (
    echo pnpm is available
)

REM Check Redis
echo [3/8] Checking Redis...
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo WARNING: Redis is not running.
    echo Please start Redis server manually or install it.
    echo You can download Redis from: https://github.com/microsoftarchive/redis/releases
    echo Or use WSL2 with Ubuntu and install redis-server
    pause
)

REM Setup API environment
echo [4/8] Setting up API environment...
cd apps\api
if not exist .env (
    echo Creating API .env file...
    (
        echo # Firecrawl API Local Development Configuration
        echo PORT=3002
        echo HOST=0.0.0.0
        echo NODE_ENV=development
        echo.
        echo # Disable database authentication for local dev
        echo USE_DB_AUTHENTICATION=false
        echo.
        echo # Redis configuration
        echo REDIS_URL=redis://localhost:6379
        echo REDIS_RATE_LIMIT_URL=redis://localhost:6379
        echo.
        echo # Playwright service
        echo PLAYWRIGHT_MICROSERVICE_URL=http://localhost:3000/scrape
        echo.
        echo # Queue management
        echo BULL_AUTH_KEY=local-dev-key
        echo.
        echo # Optional: AI features
        echo # OPENAI_API_KEY=your-openai-key
        echo.
        echo # Logging
        echo LOGGING_LEVEL=debug
    ) > .env
    echo API .env file created
)

REM Install API dependencies
echo [5/8] Installing API dependencies...
if not exist node_modules (
    echo Installing API dependencies...
    pnpm install
) else (
    echo API dependencies already installed
)

REM Setup Frontend environment
echo [6/8] Setting up Frontend environment...
cd ..\firecrawl-dashboard
if not exist .env.local (
    echo Creating Frontend .env.local file...
    (
        echo # Firecrawl Dashboard Local Development
        echo VITE_FIRECRAWL_API_URL=http://localhost:3002
        echo VITE_APP_TITLE=Firecrawl Dashboard ^(开发版^)
        echo VITE_APP_DESCRIPTION=本地开发环境
        echo NODE_ENV=development
    ) > .env.local
    echo Frontend .env.local file created
)

REM Install Frontend dependencies
echo [7/8] Installing Frontend dependencies...
if not exist node_modules (
    echo Installing Frontend dependencies...
    npm install
) else (
    echo Frontend dependencies already installed
)

REM Setup Playwright service
echo [8/8] Setting up Playwright service...
cd ..\playwright-service-ts
if not exist node_modules (
    echo Installing Playwright dependencies...
    pnpm install
    echo Installing Playwright browsers...
    pnpm exec playwright install
) else (
    echo Playwright dependencies already installed
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo To start the services, run these commands in separate terminals:
echo.
echo 1. Start Redis:
echo    redis-server
echo.
echo 2. Start Playwright service:
echo    cd apps\playwright-service-ts
echo    pnpm start
echo.
echo 3. Start API service:
echo    cd apps\api
echo    pnpm run dev
echo.
echo 4. Start Frontend:
echo    cd apps\firecrawl-dashboard
echo    npm run dev
echo.
echo Access URLs:
echo   Frontend: http://localhost:3001
echo   API:      http://localhost:3002
echo.
pause
