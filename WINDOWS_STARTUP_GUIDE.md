# Windows 系统启动 Firecrawl 指南

## 🚀 快速启动（推荐方法）

### 方法一：使用新的启动脚本

```cmd
# 在项目根目录运行
start-firecrawl-windows.bat
```

这个脚本已经修复了编码问题，应该可以正常运行。

### 方法二：手动启动步骤

如果脚本仍有问题，请按以下步骤手动启动：

#### 1. 检查 Docker 环境

```cmd
# 检查 Docker 是否运行
docker --version
docker-compose --version
```

如果提示命令不存在，请安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)。

#### 2. 创建配置文件

在项目根目录创建 `.env` 文件：

```cmd
# 创建 .env 文件
echo # Firecrawl Local Configuration > .env
echo PORT=3002 >> .env
echo DASHBOARD_PORT=3001 >> .env
echo USE_DB_AUTHENTICATION=false >> .env
echo BULL_AUTH_KEY=local-key >> .env
echo LOGGING_LEVEL=info >> .env
```

#### 3. 创建前端配置

```cmd
# 创建前端配置文件
echo VITE_FIRECRAWL_API_URL=http://localhost:3002 > apps\firecrawl-dashboard\.env
echo VITE_APP_TITLE=Firecrawl Dashboard Local >> apps\firecrawl-dashboard\.env
```

#### 4. 启动服务

```cmd
# 停止可能运行的旧服务
docker-compose -f docker-compose.local.yaml down

# 启动所有服务
docker-compose -f docker-compose.local.yaml up -d --build
```

#### 5. 验证启动

```cmd
# 查看服务状态
docker-compose -f docker-compose.local.yaml ps

# 查看日志（如果有问题）
docker-compose -f docker-compose.local.yaml logs
```

## 📱 访问地址

启动成功后：

- **前端界面**: http://localhost:3001
- **API 服务**: http://localhost:3002

## 🔧 常用命令

```cmd
# 查看所有服务状态
docker-compose -f docker-compose.local.yaml ps

# 查看实时日志
docker-compose -f docker-compose.local.yaml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.local.yaml logs api
docker-compose -f docker-compose.local.yaml logs firecrawl-dashboard

# 重启所有服务
docker-compose -f docker-compose.local.yaml restart

# 停止所有服务
docker-compose -f docker-compose.local.yaml down

# 完全清理（删除数据）
docker-compose -f docker-compose.local.yaml down -v
```

## 🛠️ 故障排除

### 问题1：端口被占用

如果看到端口占用错误：

```cmd
# 查看端口占用
netstat -ano | findstr :3001
netstat -ano | findstr :3002

# 修改端口（编辑 .env 文件）
echo PORT=3003 >> .env
echo DASHBOARD_PORT=3004 >> .env
```

### 问题2：Docker 服务启动失败

```cmd
# 查看详细错误
docker-compose -f docker-compose.local.yaml logs

# 重新构建
docker-compose -f docker-compose.local.yaml build --no-cache

# 清理 Docker 缓存
docker system prune -f
```

### 问题3：前端无法访问

检查 `apps\firecrawl-dashboard\.env` 文件内容：

```env
VITE_FIRECRAWL_API_URL=http://localhost:3002
VITE_APP_TITLE=Firecrawl Dashboard Local
```

## 💡 使用提示

1. **首次启动**：第一次启动可能需要几分钟下载镜像
2. **无需密钥**：本地部署不需要任何 API 密钥
3. **数据持久化**：数据会保存在 Docker 卷中
4. **完全离线**：启动后可以断网使用

## 🎯 验证安装

1. 打开浏览器访问 http://localhost:3001
2. 应该看到 Firecrawl Dashboard 界面
3. 在设置页面测试 API 连接（无需输入密钥）
4. 尝试抓取一个网页，如 `https://example.com`

## 📞 获取帮助

如果仍有问题，请提供以下信息：

1. Windows 版本
2. Docker Desktop 版本
3. 错误信息截图
4. 运行 `docker-compose -f docker-compose.local.yaml logs` 的输出
