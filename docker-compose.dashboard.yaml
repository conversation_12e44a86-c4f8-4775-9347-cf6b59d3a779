# Docker Compose configuration for Firecrawl with Dash<PERSON>
# This extends the main docker-compose.yaml to include the new dashboard

version: '3.8'

services:
  # Firecrawl Dashboard - New Frontend Interface
  firecrawl-dashboard:
    build: 
      context: ./apps/firecrawl-dashboard
      dockerfile: Dockerfile
    container_name: firecrawl-dashboard
    ports:
      - "3001:80"
    environment:
      # API Configuration
      - VITE_FIRECRAWL_API_URL=http://localhost:3002
      # App Configuration
      - VITE_APP_TITLE=Firecrawl Dashboard
      - VITE_APP_DESCRIPTION=网页抓取和数据提取平台
    depends_on:
      - api
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Original Ingestion UI (keep for compatibility)
  ingestion-ui:
    build: 
      context: ./apps/ui/ingestion-ui
      dockerfile: Dockerfile
    container_name: firecrawl-ingestion-ui
    ports:
      - "3000:80"
    environment:
      - VITE_FIRECRAWL_API_URL=http://localhost:3002
    depends_on:
      - api
    networks:
      - app-network
    restart: unless-stopped
    profiles:
      - legacy  # Only start with --profile legacy

  # Nginx Reverse Proxy (optional)
  nginx-proxy:
    image: nginx:alpine
    container_name: firecrawl-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates (if available)
    depends_on:
      - firecrawl-dashboard
      - api
    networks:
      - app-network
    restart: unless-stopped
    profiles:
      - proxy  # Only start with --profile proxy

networks:
  app-network:
    external: true  # Use existing network from main docker-compose.yaml
