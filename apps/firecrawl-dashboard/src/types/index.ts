// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Metadata Types
export interface Metadata {
  title?: string
  description?: string
  language?: string
  sourceURL: string
  statusCode: number
  error?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  keywords?: string
  robots?: string
  [key: string]: any
}

// Scrape Types
export interface ScrapeOptions {
  formats?: ('markdown' | 'html' | 'rawHtml' | 'content' | 'links' | 'screenshot')[]
  headers?: Record<string, string>
  includeTags?: string[]
  excludeTags?: string[]
  onlyMainContent?: boolean
  timeout?: number
  waitFor?: number
  actions?: Action[]
}

export interface Action {
  type: 'wait' | 'click' | 'write' | 'press' | 'scroll' | 'screenshot'
  selector?: string
  text?: string
  key?: string
  milliseconds?: number
  coordinate?: [number, number]
}

export interface ScrapeRequest {
  url: string
  formats?: string[]
  headers?: Record<string, string>
  includeTags?: string[]
  excludeTags?: string[]
  onlyMainContent?: boolean
  timeout?: number
  waitFor?: number
  actions?: Action[]
}

export interface ScrapeResult {
  markdown?: string
  html?: string
  rawHtml?: string
  content?: string
  links?: string[]
  screenshot?: string
  metadata: Metadata
}

// Crawl Types
export interface CrawlOptions {
  includePaths?: string[]
  excludePaths?: string[]
  maxDepth?: number
  limit?: number
  allowBackwardLinks?: boolean
  allowExternalLinks?: boolean
  ignoreSitemap?: boolean
}

export interface CrawlRequest {
  url: string
  limit?: number
  scrapeOptions?: ScrapeOptions
  includePaths?: string[]
  excludePaths?: string[]
  maxDepth?: number
  allowBackwardLinks?: boolean
  allowExternalLinks?: boolean
  ignoreSitemap?: boolean
}

export interface CrawlResponse {
  success: boolean
  id: string
  url: string
}

export interface CrawlStatus {
  status: 'scraping' | 'completed' | 'failed'
  total?: number
  completed: number
  creditsUsed: number
  expiresAt: string
  data?: ScrapeResult[]
}

// Map Types
export interface MapRequest {
  url: string
  search?: string
  ignoreSitemap?: boolean
  includeSubdomains?: boolean
  limit?: number
}

export interface MapResponse {
  status: string
  links: string[]
}

// Search Types
export interface SearchRequest {
  query: string
  limit?: number
  offset?: number
  searchOptions?: {
    country?: string
    language?: string
    location?: string
    tbs?: string
  }
  scrapeOptions?: ScrapeOptions
}

export interface SearchResult {
  url: string
  title: string
  description: string
  markdown?: string
  html?: string
  metadata?: Metadata
}

export interface SearchResponse {
  success: boolean
  data: SearchResult[]
}

// Extract Types
export interface ExtractRequest {
  urls: string[]
  prompt?: string
  schema?: Record<string, any>
  allowExternalLinks?: boolean
  timeout?: number
}

export interface ExtractResponse {
  success: boolean
  data: Record<string, any>
  warning?: string
}

// Batch Types
export interface BatchScrapeRequest {
  urls: string[]
  formats?: string[]
  headers?: Record<string, string>
  includeTags?: string[]
  excludeTags?: string[]
  onlyMainContent?: boolean
  timeout?: number
}

export interface BatchScrapeResponse {
  success: boolean
  id: string
  url: string
}

// History Types
export interface HistoryItem {
  id: string
  type: 'scrape' | 'crawl' | 'map' | 'search' | 'extract' | 'batch'
  url: string
  timestamp: number
  status: 'pending' | 'completed' | 'failed'
  result?: any
  error?: string
  creditsUsed?: number
}

// Settings Types
export interface Settings {
  apiKey: string
  apiUrl: string
  defaultTimeout: number
  defaultFormats: string[]
  theme: 'light' | 'dark' | 'system'
  autoSave: boolean
  maxHistoryItems: number
}

// WebSocket Types
export interface WebSocketMessage {
  type: 'document' | 'error' | 'done' | 'status'
  data: any
}

// UI State Types
export interface UIState {
  isLoading: boolean
  error: string | null
  currentOperation: string | null
}

// Form Types
export interface FormErrors {
  [key: string]: string | undefined
}
