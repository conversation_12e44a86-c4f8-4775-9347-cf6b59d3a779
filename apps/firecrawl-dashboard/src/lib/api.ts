import { 
  ScrapeRequest, 
  ScrapeResult, 
  CrawlRequest, 
  CrawlResponse, 
  CrawlStatus,
  MapRequest, 
  MapResponse,
  SearchRequest, 
  SearchResponse,
  ExtractRequest, 
  ExtractResponse,
  BatchScrapeRequest,
  BatchScrapeResponse,
  ApiResponse 
} from '@/types'

class FirecrawlAPI {
  private baseURL: string
  private apiKey: string

  constructor(apiKey: string, baseURL: string = 'https://api.firecrawl.dev') {
    this.apiKey = apiKey
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        ...options.headers,
      },
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP ${response.status}: ${response.statusText}`,
        }
      }

      return {
        success: true,
        data,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  // Scrape API
  async scrape(request: ScrapeRequest): Promise<ApiResponse<ScrapeResult>> {
    return this.request<ScrapeResult>('/v1/scrape', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  // Crawl API
  async crawl(request: CrawlRequest): Promise<ApiResponse<CrawlResponse>> {
    return this.request<CrawlResponse>('/v1/crawl', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  async getCrawlStatus(id: string): Promise<ApiResponse<CrawlStatus>> {
    return this.request<CrawlStatus>(`/v1/crawl/${id}`)
  }

  async cancelCrawl(id: string): Promise<ApiResponse<{ status: string }>> {
    return this.request<{ status: string }>(`/v1/crawl/${id}`, {
      method: 'DELETE',
    })
  }

  // Map API
  async map(request: MapRequest): Promise<ApiResponse<MapResponse>> {
    return this.request<MapResponse>('/v1/map', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  // Search API
  async search(request: SearchRequest): Promise<ApiResponse<SearchResponse>> {
    return this.request<SearchResponse>('/v1/search', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  // Extract API
  async extract(request: ExtractRequest): Promise<ApiResponse<ExtractResponse>> {
    return this.request<ExtractResponse>('/v1/extract', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  // Batch Scrape API
  async batchScrape(request: BatchScrapeRequest): Promise<ApiResponse<BatchScrapeResponse>> {
    return this.request<BatchScrapeResponse>('/v1/batch/scrape', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  async getBatchStatus(id: string): Promise<ApiResponse<CrawlStatus>> {
    return this.request<CrawlStatus>(`/v1/batch/scrape/${id}`)
  }

  // Health check
  async health(): Promise<ApiResponse<{ status: string }>> {
    return this.request<{ status: string }>('/health')
  }

  // Update API configuration
  updateConfig(apiKey: string, baseURL?: string) {
    this.apiKey = apiKey
    if (baseURL) {
      this.baseURL = baseURL
    }
  }
}

// Singleton instance
let apiInstance: FirecrawlAPI | null = null

export const getAPI = (apiKey?: string, baseURL?: string): FirecrawlAPI => {
  if (!apiInstance || apiKey) {
    const key = apiKey || import.meta.env.VITE_FIRECRAWL_API_KEY || ''
    const url = baseURL || import.meta.env.VITE_FIRECRAWL_API_URL || 'https://api.firecrawl.dev'
    apiInstance = new FirecrawlAPI(key, url)
  }
  return apiInstance
}

export default FirecrawlAPI
