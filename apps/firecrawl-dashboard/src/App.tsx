import { Routes, Route } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { ThemeProvider } from '@/components/theme-provider'
import Layout from '@/components/layout/Layout'
import Dashboard from '@/pages/Dashboard'
import Scrape from '@/pages/Scrape'
import Crawl from '@/pages/Crawl'
import Map from '@/pages/Map'
import Search from '@/pages/Search'
import Extract from '@/pages/Extract'
import History from '@/pages/History'
import Settings from '@/pages/Settings'

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="firecrawl-ui-theme">
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/scrape" element={<Scrape />} />
          <Route path="/crawl" element={<Crawl />} />
          <Route path="/map" element={<Map />} />
          <Route path="/search" element={<Search />} />
          <Route path="/extract" element={<Extract />} />
          <Route path="/history" element={<History />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
      <Toaster />
    </ThemeProvider>
  )
}

export default App
