import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Globe } from 'lucide-react'

export default function Crawl() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">爬取网站</h1>
        <p className="text-muted-foreground">
          爬取整个网站的所有可访问页面
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>爬取功能</span>
          </CardTitle>
          <CardDescription>
            此功能正在开发中...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            爬取功能将允许您抓取整个网站的所有页面，包括子页面和链接页面。
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
