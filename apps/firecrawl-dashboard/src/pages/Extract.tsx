import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Extract as ExtractIcon } from 'lucide-react'

export default function Extract() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">AI 提取</h1>
        <p className="text-muted-foreground">
          使用 AI 从网页中提取结构化数据
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ExtractIcon className="h-5 w-5" />
            <span>提取功能</span>
          </CardTitle>
          <CardDescription>
            此功能正在开发中...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            AI 提取功能将允许您使用自然语言提示或 JSON Schema 从网页中提取结构化数据。
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
