import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Search as SearchIcon } from 'lucide-react'

export default function Search() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">搜索网络</h1>
        <p className="text-muted-foreground">
          搜索网络并获取搜索结果的完整内容
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <SearchIcon className="h-5 w-5" />
            <span>搜索功能</span>
          </CardTitle>
          <CardDescription>
            此功能正在开发中...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            搜索功能将允许您搜索网络并获取搜索结果页面的完整内容。
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
