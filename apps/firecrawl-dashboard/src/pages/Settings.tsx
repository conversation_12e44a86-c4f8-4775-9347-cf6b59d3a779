import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useStore } from '@/store/useStore'
import { useToast } from '@/hooks/use-toast'
import { Settings as SettingsIcon, Save, TestTube } from 'lucide-react'
import { getAPI } from '@/lib/api'

export default function Settings() {
  const { settings, updateSettings } = useStore()
  const { toast } = useToast()
  const [formData, setFormData] = useState(settings)
  const [isTestingConnection, setIsTestingConnection] = useState(false)

  const handleSave = () => {
    updateSettings(formData)
    toast({
      title: '设置已保存',
      description: '您的设置已成功保存',
    })
  }

  const handleTestConnection = async () => {
    if (!formData.apiKey) {
      toast({
        title: '错误',
        description: '请先输入 API 密钥',
        variant: 'destructive',
      })
      return
    }

    setIsTestingConnection(true)
    try {
      const api = getAPI(formData.apiKey, formData.apiUrl)
      const response = await api.health()
      
      if (response.success) {
        toast({
          title: '连接成功',
          description: 'API 连接测试成功',
        })
      } else {
        throw new Error(response.error || '连接失败')
      }
    } catch (error) {
      toast({
        title: '连接失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      })
    } finally {
      setIsTestingConnection(false)
    }
  }

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">设置</h1>
        <p className="text-muted-foreground">
          配置您的 Firecrawl API 设置和偏好
        </p>
      </div>

      {/* API Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <SettingsIcon className="h-5 w-5" />
            <span>API 配置</span>
          </CardTitle>
          <CardDescription>
            配置 Firecrawl API 连接设置
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">API 密钥</label>
            <Input
              type="password"
              placeholder="fc-your-api-key"
              value={formData.apiKey}
              onChange={(e) => handleInputChange('apiKey', e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              您可以在 <a href="https://firecrawl.dev" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Firecrawl 官网</a> 获取 API 密钥
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">API URL</label>
            <Input
              placeholder="https://api.firecrawl.dev"
              value={formData.apiUrl}
              onChange={(e) => handleInputChange('apiUrl', e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              如果您使用自托管版本，请修改此 URL
            </p>
          </div>

          <div className="flex space-x-2">
            <Button onClick={handleTestConnection} disabled={isTestingConnection} variant="outline">
              {isTestingConnection ? (
                <>
                  <TestTube className="mr-2 h-4 w-4 animate-spin" />
                  测试中...
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4" />
                  测试连接
                </>
              )}
            </Button>
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Default Settings */}
      <Card>
        <CardHeader>
          <CardTitle>默认设置</CardTitle>
          <CardDescription>
            配置默认的抓取和爬取选项
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">默认超时时间 (毫秒)</label>
            <Input
              type="number"
              value={formData.defaultTimeout}
              onChange={(e) => handleInputChange('defaultTimeout', parseInt(e.target.value) || 30000)}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">历史记录最大条数</label>
            <Input
              type="number"
              value={formData.maxHistoryItems}
              onChange={(e) => handleInputChange('maxHistoryItems', parseInt(e.target.value) || 100)}
            />
          </div>

          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
