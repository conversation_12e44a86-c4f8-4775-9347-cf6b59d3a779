import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useStore } from '@/store/useStore'
import { useToast } from '@/hooks/use-toast'
import { Settings as SettingsIcon, Save, TestTube } from 'lucide-react'
import { getAPI } from '@/lib/api'

export default function Settings() {
  const { settings, updateSettings } = useStore()
  const { toast } = useToast()
  const [formData, setFormData] = useState(settings)
  const [isTestingConnection, setIsTestingConnection] = useState(false)

  const handleSave = () => {
    updateSettings(formData)
    toast({
      title: '设置已保存',
      description: '您的设置已成功保存',
    })
  }

  const isLocalDeployment = (url: string): boolean => {
    return url.includes('localhost') ||
           url.includes('127.0.0.1') ||
           url.includes('0.0.0.0') ||
           url.includes('host.docker.internal')
  }

  const handleTestConnection = async () => {
    // 本地部署时不强制要求 API 密钥
    if (!formData.apiKey && !isLocalDeployment(formData.apiUrl)) {
      toast({
        title: '错误',
        description: '请先输入 API 密钥',
        variant: 'destructive',
      })
      return
    }

    setIsTestingConnection(true)
    try {
      // 本地部署时使用默认密钥
      const testKey = formData.apiKey || 'local-test-key'
      const api = getAPI(testKey, formData.apiUrl)
      const response = await api.health()

      if (response.success) {
        toast({
          title: '连接成功',
          description: isLocalDeployment(formData.apiUrl)
            ? 'API 连接测试成功（本地部署）'
            : 'API 连接测试成功',
        })
      } else {
        throw new Error(response.error || '连接失败')
      }
    } catch (error) {
      toast({
        title: '连接失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive',
      })
    } finally {
      setIsTestingConnection(false)
    }
  }

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">设置</h1>
        <p className="text-muted-foreground">
          配置您的 Firecrawl API 设置和偏好
        </p>
      </div>

      {/* API Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <SettingsIcon className="h-5 w-5" />
            <span>API 配置</span>
          </CardTitle>
          <CardDescription>
            配置 Firecrawl API 连接设置
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">API 密钥</label>
            <Input
              type="password"
              placeholder="fc-your-api-key"
              value={formData.apiKey}
              onChange={(e) => handleInputChange('apiKey', e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              {isLocalDeployment(formData.apiUrl) ? (
                '本地部署模式：API 密钥为可选项，可以留空'
              ) : (
                <>您可以在 <a href="https://firecrawl.dev" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Firecrawl 官网</a> 获取 API 密钥</>
              )}
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">API URL</label>
            <Input
              placeholder="http://localhost:3002"
              value={formData.apiUrl}
              onChange={(e) => handleInputChange('apiUrl', e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              {isLocalDeployment(formData.apiUrl) ? (
                '本地部署地址：通常为 http://localhost:3002'
              ) : (
                '云服务地址：https://api.firecrawl.dev 或您的自托管地址'
              )}
            </p>
          </div>

          <div className="flex space-x-2">
            <Button onClick={handleTestConnection} disabled={isTestingConnection} variant="outline">
              {isTestingConnection ? (
                <>
                  <TestTube className="mr-2 h-4 w-4 animate-spin" />
                  测试中...
                </>
              ) : (
                <>
                  <TestTube className="mr-2 h-4 w-4" />
                  测试连接
                </>
              )}
            </Button>
            <Button onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Default Settings */}
      <Card>
        <CardHeader>
          <CardTitle>默认设置</CardTitle>
          <CardDescription>
            配置默认的抓取和爬取选项
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">默认超时时间 (毫秒)</label>
            <Input
              type="number"
              value={formData.defaultTimeout}
              onChange={(e) => handleInputChange('defaultTimeout', parseInt(e.target.value) || 30000)}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">历史记录最大条数</label>
            <Input
              type="number"
              value={formData.maxHistoryItems}
              onChange={(e) => handleInputChange('maxHistoryItems', parseInt(e.target.value) || 100)}
            />
          </div>

          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
