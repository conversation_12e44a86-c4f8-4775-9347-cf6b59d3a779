import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useStore } from '@/store/useStore'
import { formatRelativeTime, extractDomain } from '@/lib/utils'
import { History as HistoryIcon, Trash2, Download } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function History() {
  const { history, removeHistoryItem, clearHistory } = useStore()
  const { toast } = useToast()

  const handleClearHistory = () => {
    clearHistory()
    toast({
      title: '历史记录已清空',
      description: '所有历史记录已被删除',
    })
  }

  const handleRemoveItem = (id: string) => {
    removeHistoryItem(id)
    toast({
      title: '记录已删除',
      description: '历史记录项已被删除',
    })
  }

  const handleExportHistory = () => {
    const data = JSON.stringify(history, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `firecrawl-history-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">历史记录</h1>
          <p className="text-muted-foreground">
            查看您的抓取和爬取历史记录
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportHistory} disabled={history.length === 0}>
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button variant="destructive" onClick={handleClearHistory} disabled={history.length === 0}>
            <Trash2 className="mr-2 h-4 w-4" />
            清空历史
          </Button>
        </div>
      </div>

      {history.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <HistoryIcon className="h-5 w-5" />
              <span>暂无历史记录</span>
            </CardTitle>
            <CardDescription>
              您还没有执行过任何抓取或爬取操作
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <div className="space-y-4">
          {history.map((item) => (
            <Card key={item.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`h-3 w-3 rounded-full ${
                        item.status === 'completed'
                          ? 'bg-green-500'
                          : item.status === 'failed'
                          ? 'bg-red-500'
                          : 'bg-yellow-500'
                      }`}
                    />
                    <div>
                      <CardTitle className="text-lg">{item.type.toUpperCase()}</CardTitle>
                      <CardDescription>
                        {extractDomain(item.url)} • {formatRelativeTime(item.timestamp)}
                      </CardDescription>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveItem(item.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-sm">
                    <span className="font-medium">URL:</span> {item.url}
                  </div>
                  <div className="text-sm">
                    <span className="font-medium">状态:</span>{' '}
                    <span
                      className={
                        item.status === 'completed'
                          ? 'text-green-600'
                          : item.status === 'failed'
                          ? 'text-red-600'
                          : 'text-yellow-600'
                      }
                    >
                      {item.status === 'completed'
                        ? '已完成'
                        : item.status === 'failed'
                        ? '失败'
                        : '进行中'}
                    </span>
                  </div>
                  {item.error && (
                    <div className="text-sm">
                      <span className="font-medium">错误:</span>{' '}
                      <span className="text-red-600">{item.error}</span>
                    </div>
                  )}
                  {item.creditsUsed && (
                    <div className="text-sm">
                      <span className="font-medium">消耗积分:</span> {item.creditsUsed}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
