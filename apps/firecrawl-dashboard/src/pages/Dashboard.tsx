import { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useStore } from '@/store/useStore'
import { getAPI } from '@/lib/api'
import { formatRelativeTime } from '@/lib/utils'
import {
  FileText,
  Globe,
  Map,
  Search,
  Extract,
  Activity,
  Clock,
  TrendingUp,
  AlertCircle,
} from 'lucide-react'

export default function Dashboard() {
  const { settings, history, activeJobs } = useStore()
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('checking')

  useEffect(() => {
    const checkApiStatus = async () => {
      if (!settings.apiKey) {
        setApiStatus('offline')
        return
      }

      try {
        const api = getAPI(settings.apiKey, settings.apiUrl)
        const response = await api.health()
        setApiStatus(response.success ? 'online' : 'offline')
      } catch {
        setApiStatus('offline')
      }
    }

    checkApiStatus()
  }, [settings.apiKey, settings.apiUrl])

  const recentHistory = history.slice(0, 5)
  const activeJobsArray = Array.from(activeJobs.entries())

  const stats = {
    totalJobs: history.length,
    completedJobs: history.filter(item => item.status === 'completed').length,
    failedJobs: history.filter(item => item.status === 'failed').length,
    activeJobs: activeJobsArray.length,
  }

  const quickActions = [
    {
      title: '抓取页面',
      description: '抓取单个网页内容',
      icon: FileText,
      href: '/scrape',
      color: 'text-blue-600',
    },
    {
      title: '爬取网站',
      description: '爬取整个网站的所有页面',
      icon: Globe,
      href: '/crawl',
      color: 'text-green-600',
    },
    {
      title: '映射链接',
      description: '快速获取网站的所有链接',
      icon: Map,
      href: '/map',
      color: 'text-purple-600',
    },
    {
      title: '搜索网络',
      description: '搜索并获取搜索结果内容',
      icon: Search,
      href: '/search',
      color: 'text-orange-600',
    },
    {
      title: 'AI 提取',
      description: '使用 AI 提取结构化数据',
      icon: Extract,
      href: '/extract',
      color: 'text-red-600',
    },
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">仪表板</h1>
        <p className="text-muted-foreground">
          欢迎使用 Firecrawl Dashboard，您的网页抓取和数据提取平台
        </p>
      </div>

      {/* API Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>API 状态</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <div
              className={`h-3 w-3 rounded-full ${
                apiStatus === 'online'
                  ? 'bg-green-500'
                  : apiStatus === 'offline'
                  ? 'bg-red-500'
                  : 'bg-yellow-500 animate-pulse'
              }`}
            />
            <span className="text-sm">
              {apiStatus === 'online'
                ? 'API 服务正常'
                : apiStatus === 'offline'
                ? 'API 服务离线'
                : '检查中...'}
            </span>
            {!settings.apiKey && (
              <div className="flex items-center space-x-1 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">未配置 API 密钥</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总任务数</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalJobs}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completedJobs}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">失败</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.failedJobs}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.activeJobs}</div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold mb-4">快速操作</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action) => (
            <Card key={action.href} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <action.icon className={`h-5 w-5 ${action.color}`} />
                  <span>{action.title}</span>
                </CardTitle>
                <CardDescription>{action.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link to={action.href}>开始使用</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 lg:grid-cols-2">
        {/* Recent History */}
        <Card>
          <CardHeader>
            <CardTitle>最近活动</CardTitle>
            <CardDescription>最近的抓取和爬取任务</CardDescription>
          </CardHeader>
          <CardContent>
            {recentHistory.length > 0 ? (
              <div className="space-y-3">
                {recentHistory.map((item) => (
                  <div key={item.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`h-2 w-2 rounded-full ${
                          item.status === 'completed'
                            ? 'bg-green-500'
                            : item.status === 'failed'
                            ? 'bg-red-500'
                            : 'bg-yellow-500'
                        }`}
                      />
                      <div>
                        <p className="text-sm font-medium">{item.type}</p>
                        <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                          {item.url}
                        </p>
                      </div>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatRelativeTime(item.timestamp)}
                    </span>
                  </div>
                ))}
                <Button asChild variant="outline" className="w-full">
                  <Link to="/history">查看全部</Link>
                </Button>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">暂无活动记录</p>
            )}
          </CardContent>
        </Card>

        {/* Active Jobs */}
        <Card>
          <CardHeader>
            <CardTitle>进行中的任务</CardTitle>
            <CardDescription>当前正在执行的任务</CardDescription>
          </CardHeader>
          <CardContent>
            {activeJobsArray.length > 0 ? (
              <div className="space-y-3">
                {activeJobsArray.map(([id, job]) => (
                  <div key={id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse" />
                      <div>
                        <p className="text-sm font-medium">{job.type}</p>
                        <p className="text-xs text-muted-foreground">ID: {id}</p>
                      </div>
                    </div>
                    <span className="text-xs text-muted-foreground">{job.status}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">暂无进行中的任务</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
