import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useStore } from '@/store/useStore'
import { getAPI } from '@/lib/api'
import { ScrapeRequest, ScrapeResult } from '@/types'
import { useToast } from '@/hooks/use-toast'
import { isValidUrl, isLocalDeployment } from '@/lib/utils'
import { FileText, Loader2, Download, Copy } from 'lucide-react'

export default function Scrape() {
  const [url, setUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<ScrapeResult | null>(null)
  const { settings, addHistoryItem } = useStore()
  const { toast } = useToast()

  const handleScrape = async () => {
    if (!url.trim()) {
      toast({
        title: '错误',
        description: '请输入要抓取的 URL',
        variant: 'destructive',
      })
      return
    }

    if (!isValidUrl(url)) {
      toast({
        title: '错误',
        description: '请输入有效的 URL',
        variant: 'destructive',
      })
      return
    }

    // 本地部署时不强制要求 API 密钥
    if (!settings.apiKey && !isLocalDeployment(settings.apiUrl)) {
      toast({
        title: '错误',
        description: '请先在设置中配置 API 密钥',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      // 使用默认密钥进行本地开发
      const apiKey = settings.apiKey || 'local-dev-key'
      const api = getAPI(apiKey, settings.apiUrl)
      const request: ScrapeRequest = {
        url: url.trim(),
        formats: ['markdown', 'html'],
      }

      const response = await api.scrape(request)

      if (response.success && response.data) {
        setResult(response.data)
        addHistoryItem({
          type: 'scrape',
          url: url.trim(),
          status: 'completed',
          result: response.data,
        })
        toast({
          title: '成功',
          description: '页面抓取完成',
        })
      } else {
        throw new Error(response.error || '抓取失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      addHistoryItem({
        type: 'scrape',
        url: url.trim(),
        status: 'failed',
        error: errorMessage,
      })
      toast({
        title: '抓取失败',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopyMarkdown = async () => {
    if (result?.markdown) {
      try {
        await navigator.clipboard.writeText(result.markdown)
        toast({
          title: '已复制',
          description: 'Markdown 内容已复制到剪贴板',
        })
      } catch {
        toast({
          title: '复制失败',
          description: '无法复制到剪贴板',
          variant: 'destructive',
        })
      }
    }
  }

  const handleDownloadMarkdown = () => {
    if (result?.markdown) {
      const blob = new Blob([result.markdown], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `scraped-${Date.now()}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">抓取页面</h1>
        <p className="text-muted-foreground">
          抓取单个网页内容并转换为 Markdown 格式
        </p>
      </div>

      {/* Input Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>输入 URL</span>
          </CardTitle>
          <CardDescription>
            输入要抓取的网页 URL，系统将自动提取页面内容
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleScrape()}
              disabled={isLoading}
            />
            <Button onClick={handleScrape} disabled={isLoading || !url.trim()}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  抓取中...
                </>
              ) : (
                '开始抓取'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {result && (
        <div className="space-y-4">
          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>页面信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2 text-sm">
                <div className="grid grid-cols-4 gap-4">
                  <span className="font-medium">标题:</span>
                  <span className="col-span-3">{result.metadata.title || '无标题'}</span>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  <span className="font-medium">描述:</span>
                  <span className="col-span-3">{result.metadata.description || '无描述'}</span>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  <span className="font-medium">语言:</span>
                  <span className="col-span-3">{result.metadata.language || '未知'}</span>
                </div>
                <div className="grid grid-cols-4 gap-4">
                  <span className="font-medium">状态码:</span>
                  <span className="col-span-3">{result.metadata.statusCode}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>抓取内容</CardTitle>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={handleCopyMarkdown}>
                    <Copy className="mr-2 h-4 w-4" />
                    复制
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleDownloadMarkdown}>
                    <Download className="mr-2 h-4 w-4" />
                    下载
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {result.markdown ? (
                <div className="space-y-4">
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="whitespace-pre-wrap text-sm overflow-auto max-h-96">
                      {result.markdown}
                    </pre>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">无内容</p>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
