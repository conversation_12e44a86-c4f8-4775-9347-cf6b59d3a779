import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Map as MapIcon } from 'lucide-react'

export default function Map() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">映射链接</h1>
        <p className="text-muted-foreground">
          快速获取网站的所有链接
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapIcon className="h-5 w-5" />
            <span>映射功能</span>
          </CardTitle>
          <CardDescription>
            此功能正在开发中...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            映射功能将允许您快速获取网站的所有链接，无需实际抓取内容。
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
