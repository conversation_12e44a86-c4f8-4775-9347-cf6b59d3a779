import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Settings, HistoryItem, UIState } from '@/types'

interface AppState {
  // Settings
  settings: Settings
  updateSettings: (settings: Partial<Settings>) => void
  
  // History
  history: HistoryItem[]
  addHistoryItem: (item: Omit<HistoryItem, 'id' | 'timestamp'>) => void
  updateHistoryItem: (id: string, updates: Partial<HistoryItem>) => void
  removeHistoryItem: (id: string) => void
  clearHistory: () => void
  
  // UI State
  ui: UIState
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setCurrentOperation: (operation: string | null) => void
  
  // Active Jobs
  activeJobs: Map<string, { type: string; status: string }>
  addActiveJob: (id: string, type: string) => void
  updateJobStatus: (id: string, status: string) => void
  removeActiveJob: (id: string) => void
}

const defaultSettings: Settings = {
  apiKey: '',
  apiUrl: 'https://api.firecrawl.dev',
  defaultTimeout: 30000,
  defaultFormats: ['markdown'],
  theme: 'light',
  autoSave: true,
  maxHistoryItems: 100,
}

const defaultUIState: UIState = {
  isLoading: false,
  error: null,
  currentOperation: null,
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Settings
      settings: defaultSettings,
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),

      // History
      history: [],
      addHistoryItem: (item) => {
        const newItem: HistoryItem = {
          ...item,
          id: crypto.randomUUID(),
          timestamp: Date.now(),
        }
        
        set((state) => {
          const newHistory = [newItem, ...state.history]
          // Keep only the latest maxHistoryItems
          if (newHistory.length > state.settings.maxHistoryItems) {
            newHistory.splice(state.settings.maxHistoryItems)
          }
          return { history: newHistory }
        })
      },
      
      updateHistoryItem: (id, updates) =>
        set((state) => ({
          history: state.history.map((item) =>
            item.id === id ? { ...item, ...updates } : item
          ),
        })),
      
      removeHistoryItem: (id) =>
        set((state) => ({
          history: state.history.filter((item) => item.id !== id),
        })),
      
      clearHistory: () => set({ history: [] }),

      // UI State
      ui: defaultUIState,
      setLoading: (loading) =>
        set((state) => ({
          ui: { ...state.ui, isLoading: loading },
        })),
      
      setError: (error) =>
        set((state) => ({
          ui: { ...state.ui, error },
        })),
      
      setCurrentOperation: (operation) =>
        set((state) => ({
          ui: { ...state.ui, currentOperation: operation },
        })),

      // Active Jobs
      activeJobs: new Map(),
      addActiveJob: (id, type) =>
        set((state) => {
          const newActiveJobs = new Map(state.activeJobs)
          newActiveJobs.set(id, { type, status: 'pending' })
          return { activeJobs: newActiveJobs }
        }),
      
      updateJobStatus: (id, status) =>
        set((state) => {
          const newActiveJobs = new Map(state.activeJobs)
          const job = newActiveJobs.get(id)
          if (job) {
            newActiveJobs.set(id, { ...job, status })
          }
          return { activeJobs: newActiveJobs }
        }),
      
      removeActiveJob: (id) =>
        set((state) => {
          const newActiveJobs = new Map(state.activeJobs)
          newActiveJobs.delete(id)
          return { activeJobs: newActiveJobs }
        }),
    }),
    {
      name: 'firecrawl-dashboard-storage',
      partialize: (state) => ({
        settings: state.settings,
        history: state.history,
      }),
    }
  )
)
