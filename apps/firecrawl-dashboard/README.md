# Firecrawl Dashboard

一个现代化的 Firecrawl 前端界面，提供完整的网页抓取和数据提取功能。

## 功能特性

### 🔥 核心功能
- **抓取页面** - 抓取单个网页内容并转换为 Markdown
- **爬取网站** - 爬取整个网站的所有可访问页面
- **映射链接** - 快速获取网站的所有链接
- **搜索网络** - 搜索并获取搜索结果内容
- **AI 提取** - 使用 AI 提取结构化数据

### 🛠️ 技术特性
- **现代化技术栈** - React 18 + TypeScript + Vite
- **响应式设计** - 支持桌面端和移动端
- **实时状态更新** - WebSocket 连接支持
- **安全的 API 管理** - 环境变量和安全配置
- **历史记录管理** - 本地存储和导出功能
- **多格式支持** - Markdown、JSON、HTML 等格式

### 🎨 用户体验
- **直观的操作界面** - 基于 shadcn/ui 组件库
- **深色/浅色主题** - 自动适应系统主题
- **错误处理** - 完善的错误提示和重试机制
- **进度显示** - 实时显示任务进度
- **结果展示** - 多种格式的结果展示

## 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境变量模板：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置您的 Firecrawl API 密钥：

```env
VITE_FIRECRAWL_API_KEY=fc-your-api-key-here
VITE_FIRECRAWL_API_URL=https://api.firecrawl.dev
```

### 开发模式

```bash
npm run dev
```

应用将在 `http://localhost:3001` 启动。

### 生产构建

```bash
npm run build
```

构建文件将输出到 `dist` 目录。

## Docker 部署

### 构建镜像

```bash
docker build -t firecrawl-dashboard .
```

### 运行容器

```bash
docker run -p 80:80 firecrawl-dashboard
```

### Docker Compose

创建 `docker-compose.yml`：

```yaml
version: '3.8'
services:
  firecrawl-dashboard:
    build: .
    ports:
      - "3001:80"
    environment:
      - VITE_FIRECRAWL_API_KEY=fc-your-api-key
      - VITE_FIRECRAWL_API_URL=http://localhost:3002
    depends_on:
      - firecrawl-api
```

## 集成到现有项目

### 更新主项目的 Docker Compose

在主项目的 `docker-compose.yaml` 中添加：

```yaml
services:
  # ... 现有服务

  firecrawl-dashboard:
    build: ./apps/firecrawl-dashboard
    ports:
      - "3001:80"
    environment:
      - VITE_FIRECRAWL_API_URL=http://localhost:3002
    depends_on:
      - api
    networks:
      - app-network
```

### 环境变量配置

在生产环境中，建议通过环境变量或配置文件管理 API 密钥：

```bash
# 通过环境变量
export VITE_FIRECRAWL_API_KEY=your-api-key

# 或在 .env 文件中
echo "VITE_FIRECRAWL_API_KEY=your-api-key" > .env
```

## 功能说明

### 仪表板
- 显示 API 状态和连接信息
- 统计数据概览（总任务、完成、失败、进行中）
- 快速操作入口
- 最近活动和进行中的任务

### 抓取页面
- 输入 URL 进行单页抓取
- 支持多种输出格式
- 实时显示抓取进度
- 结果预览和下载

### 爬取网站
- 配置爬取参数（深度、限制、路径过滤）
- 实时监控爬取进度
- 批量结果管理
- WebSocket 实时更新

### 历史记录
- 查看所有操作历史
- 按类型和状态筛选
- 导出历史数据
- 删除和清理功能

### 设置
- API 密钥和 URL 配置
- 连接测试功能
- 默认参数设置
- 主题和偏好设置

## 安全考虑

### API 密钥管理
- 支持环境变量配置
- 不在客户端硬编码密钥
- 支持代理模式保护密钥

### CORS 配置
- 正确配置跨域请求
- 限制允许的来源域名

### 生产部署
- 使用 HTTPS
- 配置安全头部
- 定期更新依赖

## 开发指南

### 项目结构

```
src/
├── components/          # UI 组件
│   ├── ui/             # 基础 UI 组件
│   └── layout/         # 布局组件
├── pages/              # 页面组件
├── hooks/              # 自定义 Hooks
├── lib/                # 工具库
├── store/              # 状态管理
├── types/              # TypeScript 类型
└── styles/             # 样式文件
```

### 添加新功能

1. 在 `src/types/` 中定义类型
2. 在 `src/lib/api.ts` 中添加 API 方法
3. 创建页面组件
4. 更新路由配置
5. 添加导航菜单项

### 自定义主题

修改 `src/index.css` 中的 CSS 变量：

```css
:root {
  --primary: your-primary-color;
  --secondary: your-secondary-color;
  /* ... */
}
```

## 故障排除

### 常见问题

1. **API 连接失败**
   - 检查 API 密钥是否正确
   - 确认 API URL 是否可访问
   - 检查网络连接和防火墙设置

2. **构建失败**
   - 清除 node_modules 并重新安装
   - 检查 Node.js 版本是否符合要求
   - 确认所有依赖都已正确安装

3. **Docker 部署问题**
   - 检查 Dockerfile 和 nginx 配置
   - 确认端口映射正确
   - 查看容器日志排查问题

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
