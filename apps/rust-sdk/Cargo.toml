[package]
name = "firecrawl"
author= "Mendable.ai"
version = "1.2.0"
edition = "2021"
license = "MIT"
homepage = "https://www.firecrawl.dev/"
repository ="https://github.com/mendableai/firecrawl"
description = "Rust SDK for Firecrawl API."
authors = ["G<PERSON><PERSON><PERSON> <<EMAIL>>", "sanix-darker <<EMAIL>>", "kkhar<PERSON> <<EMAIL>>"]

[lib]
path = "src/lib.rs"
name = "firecrawl"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]
reqwest = { version = "0.12.22", features = ["json", "blocking"] }
serde = { version = "^1.0", features = ["derive"] }
serde_json = "^1.0"
serde_with = "^3.9"
log = "^0.4"
thiserror = "^1.0"
uuid = { version = "^1.10", features = ["v4"] }
tokio = { version = "^1", features = ["full"] }
futures = "0.3.31"
schemars = "0.8.22"

[dev-dependencies]
clippy = "^0.0.302"
assert_matches = "^1.5"
dotenvy = "^0.15"
tokio = { version = "1", features = ["full"] }
mockito = "1.7.0"
clap = { version ="4.5.35", features = ["derive"] }
axum = { version = "0.8.3", features = ["tokio", "macros"] }
bat = "0.25.0"

[build-dependencies]
tokio = { version = "1", features = ["full"] }
