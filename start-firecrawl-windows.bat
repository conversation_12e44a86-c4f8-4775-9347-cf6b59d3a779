@echo off
chcp 65001 >nul
echo ========================================
echo    Firecrawl Local Deployment
echo ========================================
echo.

REM Check Docker
echo [1/6] Checking Docker installation...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not running.
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)
echo Docker is installed and running.

REM Check Docker Compose
echo [2/6] Checking Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker Compose is not available.
    pause
    exit /b 1
)
echo Docker Compose is available.

REM Create .env file
echo [3/6] Creating configuration files...
if not exist .env (
    echo Creating .env file...
    (
        echo # Firecrawl Local Configuration
        echo PORT=3002
        echo DASHBOARD_PORT=3001
        echo USE_DB_AUTHENTICATION=false
        echo BULL_AUTH_KEY=local-key-%RANDOM%
        echo LOGGING_LEVEL=info
    ) > .env
    echo .env file created successfully.
) else (
    echo .env file already exists.
)

REM Create frontend .env file
if not exist apps\firecrawl-dashboard\.env (
    echo Creating frontend configuration...
    if exist apps\firecrawl-dashboard\.env.local (
        copy apps\firecrawl-dashboard\.env.local apps\firecrawl-dashboard\.env >nul
        echo Frontend configuration created.
    ) else (
        echo Creating default frontend configuration...
        (
            echo VITE_FIRECRAWL_API_URL=http://localhost:3002
            echo VITE_APP_TITLE=Firecrawl Dashboard Local
        ) > apps\firecrawl-dashboard\.env
    )
) else (
    echo Frontend configuration already exists.
)

REM Stop existing services
echo [4/6] Stopping any existing services...
docker-compose -f docker-compose.local.yaml down >nul 2>&1

REM Start services
echo [5/6] Starting Firecrawl services...
echo This may take a few minutes for the first time...
docker-compose -f docker-compose.local.yaml up -d --build

if errorlevel 1 (
    echo ERROR: Failed to start services.
    echo Please check the error messages above.
    pause
    exit /b 1
)

REM Wait for services
echo [6/6] Waiting for services to be ready...
timeout /t 15 /nobreak >nul

REM Check if services are running
echo Checking service status...
docker-compose -f docker-compose.local.yaml ps

echo.
echo ========================================
echo    Startup Complete!
echo ========================================
echo.
echo Access your Firecrawl installation:
echo   Frontend Dashboard: http://localhost:3001
echo   API Service:        http://localhost:3002
echo.
echo Useful commands:
echo   View logs:    docker-compose -f docker-compose.local.yaml logs -f
echo   Stop all:     docker-compose -f docker-compose.local.yaml down
echo   Restart:      docker-compose -f docker-compose.local.yaml restart
echo.
echo Note: No API key is required for local deployment!
echo.
echo Press any key to open the dashboard in your browser...
pause >nul

REM Try to open browser
start http://localhost:3001
